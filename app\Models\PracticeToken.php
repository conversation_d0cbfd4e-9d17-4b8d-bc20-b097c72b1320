<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PracticeToken extends Model
{
    use HasFactory;

    //TABLE
    public $table = 'practice_tokens';

    //FILLABLES
    protected $fillable = [
        'practice_id',
        'token',
    ];

    //HIDDEN
    protected $hidden = [];

    //APPENDS
    protected $appends = [];

    //WITH
    protected $with = [];

    //CASTS
    protected $casts = [];

    //RULES
    public static $getListRules = [];

    //RELATIONSHIPS
    public function practice()
    {
        return $this->belongsTo(Practice::class, 'practice_id');
    }

    //ATTRIBUTES
    //public function getExampleAttribute()
    //{
    //    return $data;
    //}
}
