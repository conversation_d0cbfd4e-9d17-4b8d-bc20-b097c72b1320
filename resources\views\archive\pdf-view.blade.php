@extends('master')

@section('content')
<div style="height: auto;">
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    @if($prescription->status === \App\Models\ImportFile::STATUS_PENDING_APPROVAL)
                    <h5 class="mb-0"><PERSON><PERSON><PERSON> successfully signed.</h5>
                    @else
                    <h5 class="mb-0"><PERSON><PERSON><PERSON> successfully created.</h5>
                    @endif
                    <div class="action-buttons">
                        <a href="{{ route('dashboard') }}" class="btn btn-secondary">
                            <i class="fas fa-home"></i> Done
                        </a>
                        <a href="{{ route('archive.file-download', ['id' => $prescription->id]) }}" class="btn btn-primary">
                            <i class="fas fa-download"></i> Download
                        </a>
                        @if($user->role == \App\Models\User::ROLE_PROVIDER && $prescription->status === \App\Models\ImportFile::STATUS_NEW)
                        <a href="{{ route('archive.sign-pdf', ['id' => $prescription->id]) }}" class="btn btn-success">
                            <i class="fas fa-pen-nib"></i> Sign
                        </a>
                        @endif
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="pdf-container" style="height: 70vh;">
                        <iframe src="{{ route('archive.show-pdf', ['id' => $prescription->id]) }}" width="100%" height="100%" frameborder="0"></iframe>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .action-buttons .btn {
        margin-left: 5px;
    }
    .pdf-container {
        width: 100%;
        overflow: hidden;
    }
</style>
@endpush

@push('scripts')
<script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
@endpush
