<div>
    @if (session()->has('message'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('message') }}
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    @endif

    <form wire:submit.prevent="saveTemplate">
        <x-layout.row>
            <div class="col">
                <x-card.body>
                    <!-- Medication Info -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="mb-3">
                                <x-form.input.text label="Template Title" labelRequired="1"
                                    model="script_template.title" />
                            </div>
                            <div class="mb-3" wire:ignore>
                                <div class="form-group">
                                    <label for="medication" class="form-label">
                                        Medication
                                    </label>
                                    <select label="medication" id="medication"
                                        class="form-control @error('script_template.medication') is-invalid @enderror gender-select"
                                        wire:model="script_template.medication" placeholder="Select medication">
                                        <option value="" selected>Select medication</option>
                                        @foreach ($medications as $medication)
                                            <option value="{{ $medication->name }}">{{ $medication->name }}</option>
                                        @endforeach
                                    </select>

                                    @error('script_template.medication')
                                        <span class="form-text text-danger"><strong>{{ $message }}</strong></span>
                                    @enderror
                                </div>
                            </div>
                            <div class="mb-3">
                                <x-form.input.text label="Refills" labelRequired="0" model="script_template.refills" />
                            </div>
                            <div class="mb-3">
                                <x-form.input.text label="Vial Quantity" labelRequired="0"
                                    model="script_template.vial_quantity" />
                            </div>
                            <div class="mb-3">
                                <x-form.input.text label="Days Supply" labelRequired="0"
                                    model="script_template.days_supply" type="number" min="0" />
                            </div>
                        </div>
                    </div>


                    <!-- Additional Information -->
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <x-form.input.textarea label="SIG" labelRequired="0" model="script_template.sig" />
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <x-form.input.textarea label="Note" labelRequired="0" model="script_template.notes"
                                    rows="3" />
                            </div>
                        </div>
                    </div>
                </x-card.body>

                <x-group.errors />

                <x-card.footer>

                    <button type="submit" class="btn btn-primary px-5" wire:loading.attr="disabled"
                        wire:target="saveTemplate">
                        <span wire:loading.remove wire:target="saveTemplate">Save</span>
                        <span wire:loading wire:target="saveTemplate" style="display: none;">
                            <i class="fas fa-spinner fa-spin mr-1"></i>
                            Saving...
                        </span>
                    </button>
                    <a href="{{ route('scripts.template') }}" class="btn btn-outline-secondary px-5">Cancel</a>

                </x-card.footer>
                {{-- </x-card> --}}
            </div>
        </x-layout.row>
    </form>

</div>

@push('scripts')
    <script>
        $('document').ready(function() {
            $('#medication').select2({
                placeholder: "Select Medication",
            }).on('change', function(e) {
                @this.set('script_template.medication', $(e.target).val());
            });
        });
    </script>
@endpush
