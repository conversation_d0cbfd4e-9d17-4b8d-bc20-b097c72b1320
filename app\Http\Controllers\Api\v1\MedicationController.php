<?php

namespace App\Http\Controllers\Api\v1;

use App\Http\Controllers\Controller;
use App\Http\Requests\MedicationListRequest;
use App\Http\Requests\v1\MedicationStoreRequest;
use App\Models\Medication;
use App\Models\PracticeMedication;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class MedicationController extends Controller
{
    public function index(MedicationListRequest $request)
    {
        $practice = Auth::user();

        // Get medication IDs that this practice has access to
        $practiceMedicationIds = PracticeMedication::where('practice_id', $practice->id)
            ->pluck('medication_id');

        $data = Medication::query()
            ->select('id', 'name', 'ndc') // Only include the fields you want
            ->whereIn('id', $practiceMedicationIds); // Filter by practice-medication relationship

        // if ($request->has('search')) {
        //     $search = '%' . $request->search . '%';
        //     $data = $data->where(function ($query) use ($search) {
        //         $query->where('name', 'like', $search);
        //     });
        // }

        if ($request->has('page')) {
            return response()->json(
                collect([
                    'status' => '1',
                    'message' => __('apiMessages.medication_list_returned'),
                    'transaction_id' => $request->transaction_id,
                ])->merge($data->simplePaginate($request->get('per_page', 10)))
            );
        }

        return response()->json([
            'data' => $data->get(),
            'transaction_id' => $request->transaction_id,
            'message' => __('apiMessages.medication_list_returned'),
            'status' => '1',
        ]);
    }


    public function store(MedicationStoreRequest $request)
    {
        $data = Medication::create(
            [
                'name' => $request->name,
                'ndc' => $request->ndc,
                'is_active' => $request->is_active ?? 1,
            ]
        );

        return response()->json([
            'data' => $data,
            'message' => __('apiMessages.medication_created_successfully'),
            'status' => '1',
        ]);
    }

    public function update(Request $request, Medication $medication)
    {
        $medication->update($request->all());

        return response()->json([
            'data' => $medication,
            'message' => __('apiMessages.medication_updated_successfully'),
            'status' => '1',
        ]);
    }

    public function destroy(Medication $medication)
    {
        $medication->delete();

        return response()->json([
            'message' => __('apiMessages.medication_deleted_successfully'),
            'status' => '1',
        ]);
    }
}
