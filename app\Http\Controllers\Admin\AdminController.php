<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\GetDatatableRequest;
use App\Http\Resources\DataTableCollection;
use App\Models\User;
use App\Services\LogService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Schema;

class AdminController extends Controller
{
    public function index()
    {
        $page_title = 'Staff';

        return view('users.admin-index', [
            'page_title' => $page_title
        ]);
    }

    public function create()
    {
        $has_back = request('return_url') ?: route('admin.index');
        $page_title = 'Create Staff User';
        $card_title = 'Staff';

        $user = app(User::class);
        $livewire_component = 'user.user-admin';
        $livewire_data = [
            'user' => $user,
            'page_title' => $page_title,
            'card_title' => $card_title
        ];
        return view('layouts.livewire', [
            'page_title' => $page_title,
            'card_title' => $card_title,
            'has_back' => $has_back,
            'livewire_component' => $livewire_component,
            'livewire_data' => $livewire_data,
        ]);
    }

    public function edit(User $user)
    {
        $has_back = request('return_url') ?: route('admin.index');
        $page_title = 'Edit Admin User';
        $card_title = 'Admin User';

        $livewire_component = 'user.user-admin';
        $livewire_data = [
            'user' => $user,
        ];

        return view('layouts.livewire', [
            'page_title' => $page_title,
            'card_title' => $card_title,
            'has_back' => $has_back,
            'livewire_component' => $livewire_component,
            'livewire_data' => $livewire_data,
        ]);
    }

    public function delete(User $user)
    {
        // Log staff deletion before deleting
        LogService::logStaffDeleted($user);

        $user->delete();
        return response([
            'message' => __('messages.User_deleted'),
            'status' => '1',
        ]);
    }

    public function indexWeb(GetDatatableRequest $request)
    {
        $data = User::query()->where('role', '!=', User::ROLE_PROVIDER);
        $search = $request->input('query.search', '');
        $sort_order = $request->input('sort.sort', '');
        $sort_field = $request->input('sort.field', '');
        if ($search) {
            $query_search = "%" . $search . "%";

            $data->where(function ($query) use ($query_search) {
                $query->where('first_name', 'like', $query_search)
                    ->orWhere('last_name', 'like', $query_search);
            });
        }

        if ($sort_order && $sort_field) {
            $userColumns = Schema::getColumnListing((new User())->table);

            if (in_array($sort_field, $userColumns)) {
                $data->orderBy($sort_field, $sort_order);
            } else {
                // Default to latest if field doesn't exist
                $data->latest();
            }
        } else {
            $data->latest();
        }
        return new DataTableCollection($data->paginate($request->pagination['perpage'], ['*'], 'page', $request->pagination['page']));
    }

    public function pagination(Request $request)
    {
        $data = User::query();

        if ($request->has('search')) {
            $search = '%' . $request->search . '%';

            $data->where(function ($query) use ($search) {
                $query = $query->where('first_name', 'like', $search)
                    ->orWhere('last_name', 'like', $search);
            });
        }
        return $data->simplePaginate()->items();
    }

    public function change_status(User $user)
    {
        $user->is_active = !$user->is_active;
        $user->save();

        // Log the status change
        if ($user->is_active == 0) {
            LogService::logStaffActivated($user);
        } else {
            LogService::logStaffDeactivated($user);
        }

        return response([
            'message' => __('messages.User_updated'),
            'status' => 1,
        ]);
    }
}
