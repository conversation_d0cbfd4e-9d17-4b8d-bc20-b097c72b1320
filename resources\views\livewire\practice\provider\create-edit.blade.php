<div>
    <form wire:submit.prevent="store">
        <x-layout.row>
            <div class="col">
                <x-card.body>
                    <div class="row">
                        <div class="col-md-12 mb-3" wire:ignore>
                            <label for="provider">Provider <span class="text-danger">*</span></label>
                           <select class="form-control" wire:model="provider_id" id="provider">
                               <option value="">Select Provider</option>
                               @foreach ($providers as $provider)
                                   <option value="{{ $provider->id }}">{{ $provider->first_name }} {{ $provider->last_name }}</option>
                               @endforeach
                           </select>
                        </div>

                    </div>
                </x-card.body>

                <x-group.errors />

                <x-card.footer>
                    <x-btn-with-loading target="store" text="Save" type="submit" />
                </x-card.footer>
            </div>
        </x-layout.row>
    </form>
</div>

@push('scripts')
<script>
    $(document).ready(function () {
        $('#provider').select2({
            placeholder: "Select Provider",
        }).on('change', function (e) {
            @this.set('provider_id', $(e.target).val());
        });
    });
</script>

@endpush