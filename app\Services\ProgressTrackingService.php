<?php
// app/Services/ProgressTrackingService.php

namespace App\Services;

use Illuminate\Support\Facades\Cache;

class ProgressTrackingService
{
    public function manageProgress(string $key, array $data = [], int $expirationMinutes = 30)
    {
        Cache::put($key, $data, now()->addMinutes($expirationMinutes));
    }

    public function updateProgress(string $key, array $data = [])
    {
        Cache::put($key, $data);
    }

    public function get(string $key, array $data = [])
    {
        return Cache::get($key,$data);
    }

    public function forget(string $key)
    {
        Cache::forget($key);
    }

    public function exists(string $key)
    {
        return Cache::has($key);
    }
}
