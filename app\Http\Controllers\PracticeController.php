<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\GetDatatableRequest;
use App\Http\Resources\DataTableCollection;
use App\Models\Practice;
use App\Models\PracticeProvider;
use App\Models\PracticeMedication;
use App\Models\PracticeToken;
use App\Services\LogService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class PracticeController extends Controller
{
    public function index()
    {
        $page_title = 'Practices';

        return view('practices.index', [
            'page_title' => $page_title,
        ]);
    }

    public function indexWeb(GetDatatableRequest $request)
    {
        $practices = Practice::query();

        $search = $request->input('query.search', '');
        $sort_order = $request->input('sort.sort', '');
        $sort_field = $request->input('sort.field', '');

        if ($search) {
            $query_search = "%" . $search . "%";

            $practices->where(function ($query) use ($query_search) {
                $query->where('name', 'like', $query_search)
                    ->orWhere('phone', 'like', $query_search)
                    ->orWhere('fax', 'like', $query_search)
                    ->orWhere('default_order_method', 'like', $query_search)
                    ->orWhere('zip', 'like', $query_search)
                    ->orWhere('city', 'like', $query_search);

            });
        }

        if ($sort_order && $sort_field) {
            $practiceColumns = Schema::getColumnListing((new Practice())->table);

            if (in_array($sort_field, $practiceColumns)) {
                $practices->orderBy($sort_field, $sort_order);
            } else {
                $practices->latest();
            }
        } else {
            $practices->latest();
        }
        return new DataTableCollection($practices->paginate($request->pagination['perpage'], ['*'], 'page', $request->pagination['page']));

    }


    public function tokens(Request $request)
    {
        $page_title = 'Tokens';
        $practice = Practice::find($request->practice);
        $has_back = request('return_url') ?: route('practices.index');
        if (!$practice) {
            return redirect()->route('practices.index')->with('error', 'Practice not found');
        }

        return view('practices.index-token', [
            'page_title' => $page_title,
            'practice' => $practice,
            'has_back' => $has_back,
        ]);
    }

    public function indexWebTokens(GetDatatableRequest $request)
    {
        $tokens = PracticeToken::query()->where('practice_id', $request->practice)->with('practice');

        $search = $request->input('query.search', '');
        $sort_order = $request->input('sort.sort', '');
        $sort_field = $request->input('sort.field', '');

        if ($search) {
            $query_search = "%" . $search . "%";

            $tokens->where(function ($query) use ($query_search) {
                $query->where('token', 'like', $query_search);
            });
        }

        if ($sort_order && $sort_field) {
            $tokenColumns = Schema::getColumnListing((new PracticeToken())->table);

            if (in_array($sort_field, $tokenColumns)) {
                $tokens->orderBy($sort_field, $sort_order);
            } else {
                $tokens->latest();
            }
        } else {
            $tokens->latest();
        }

        return new DataTableCollection($tokens->paginate($request->pagination['perpage'], ['*'], 'page', $request->pagination['page']));
    }

    public function generateToken($practice)
    {
        $practice = Practice::findOrFail($practice);

        $token = $practice->createToken('practice-token-' . $practice->id);
        $token = $token->plainTextToken;

        PracticeToken::create([
            'practice_id' => $practice->id,
            'token' => $token,
        ]);

        return response()->json([
            'status' => '1',
            'message' => 'Token generated successfully',
        ]);
    }

    public function create()
    {
        $has_back = route('practices.index');
        $page_title = 'Create Practice';

        $livewire_component = 'practice.create-edit';
        $livewire_data = [
            'page_title' => $page_title,
        ];

        return view('layouts.livewire', [
            'page_title' => $page_title,
            'has_back' => $has_back,
            'livewire_component' => $livewire_component,
            'livewire_data' => $livewire_data,
        ]);
    }

    public function edit(Practice $practice)
    {
        $has_back = route('practices.index');
        $page_title = 'Edit Practice';

        $livewire_component = 'practice.create-edit';
        $livewire_data = [
            'practice' => $practice,
            'page_title' => $page_title,
        ];

        return view('layouts.livewire', [
            'page_title' => $page_title,
            'has_back' => $has_back,
            'livewire_component' => $livewire_component,
            'livewire_data' => $livewire_data,
        ]);
    }

    public function change_status(Practice $practice)
    {
        $practice->is_active = !$practice->is_active;
        $practice->save();


        // Log the status change
        // if ($practice->is_active == 0) {
        //     LogService::logPracticeActivated($practice);
        // } else {
        //     LogService::logPracticeDeactivated($practice);
        // }

        return response([
            'message' => $practice->is_active ? __('messages.practice_deactivated') : __('messages.practice_activated'),
            'status' => 1,
        ]);
    }

    public function delete(Practice $practice)
    {
        $tokens = $practice->practiceTokens()->get();

        if ($tokens->count() > 0) {
            foreach ($tokens as $token) {
                list($tokenId) = explode('|', $token->token, 2);
                DB::table('personal_access_tokens')->where('id', $tokenId)->delete();
                $token->delete();
            }
        }

        $practice->delete();

        return response([
            'message' => __('messages.practice_deleted'),
            'status' => 1,
        ]);
    }

    public function providers(Practice $practice)
    {
        $page_title = 'Practice Providers';
        $has_back = route('practices.index');

        $providers = $practice->providers()->with('user')->get();

        return view('practices.index-providers', [
            'page_title' => $page_title,
            'practice' => $practice,
            'providers' => $providers,
            'has_back' => $has_back,
        ]);
    }

    public function indexWebProviders(GetDatatableRequest $request, Practice $practice)
    {

        $search = $request->input('query.search', '');
        $sort_order = $request->input('sort.sort', 'desc');
        $sort_field = $request->input('sort.field', 'created_at'); // Default sort field

        $query = $practice->providers()->with('user');

        if ($search) {
            $query_search = "%" . $search . "%";

            $query->where(function ($q) use ($query_search) {
                $q->where('token', 'like', $query_search);
            });
        }

        $providerColumns = Schema::getColumnListing((new PracticeProvider())->getTable());

        if (in_array($sort_field, $providerColumns)) {
            $query->orderBy($sort_field, $sort_order);
        } else {
            $query->latest();
        }

        $providers = $query->paginate($request->pagination['perpage'], ['*'], 'page', $request->pagination['page']);

        return new DataTableCollection($providers);
    }

    public function createProvider(Practice $practice){
        $page_title = 'Connect Provider';
        $has_back = route('practices.providers', $practice->id);

        $livewire_component = 'practice.provider.create-edit';
        $livewire_data = [
            'practice' => $practice,
            'practice_id' => $practice->id,
        ];

        return view('layouts.livewire', [
            'page_title' => $page_title,
            'has_back' => $has_back,
            'livewire_component' => $livewire_component,
            'livewire_data' => $livewire_data,
        ]);
    }

    public function deleteProvider(Practice $practice, $providerId)
    {

        $provider = PracticeProvider::where('provider_id', $providerId)->where('practice_id', $practice->id)->first();

        if (!$provider) {
            return response()->json([
                'message' => __('messages.provider_not_found'),
                'status' => 0,
            ]);
        }
        $provider->delete();

        return response()->json([
            'message' => __('messages.provider_deleted_successfully'),
            'status' => 1,
        ]);
    }

    public function medications(Practice $practice)
    {
        $page_title = 'Medications';
        $has_back = route('practices.index');
        $medications = PracticeMedication::where('practice_id', $practice->id)->with('medication')->get();

        return view('practices.index-medications', [
            'page_title' => $page_title,
            'has_back' => $has_back,
            'practice' => $practice,
            'medications' => $medications,
        ]);
    }

    public function indexWebMedications(GetDatatableRequest $request, Practice $practice)
    {
        $medications = PracticeMedication::where('practice_id', $practice->id)->with('medication');

        $search = $request->input('query.search', '');
        $sort_order = $request->input('sort.sort', '');
        $sort_field = $request->input('sort.field', '');

        if ($search) {
            $query_search = "%" . $search . "%";

            $medications->where(function ($query) use ($query_search) {
                $query->where('medication.name', 'like', $query_search)
                    ->orWhere('medication.ndc', 'like', $query_search);
            });
        }

        if ($sort_order && $sort_field) {
            $medicationColumns = Schema::getColumnListing((new PracticeMedication())->table);

            if (in_array($sort_field, $medicationColumns)) {
                $medications->orderBy($sort_field, $sort_order);
            } else {
                $medications->latest();
            }
        } else {
            $medications->latest();
        }

        return new DataTableCollection($medications->paginate($request->pagination['perpage'], ['*'], 'page', $request->pagination['page']));
    }

    public function createMedication(Practice $practice)
    {
        $page_title = 'Connect Medication';
        $has_back = route('practices.medications', $practice->id);
        $practice_id = $practice->id;

        $livewire_component = 'practice.medications.create-edit';
        $livewire_data = [
            'practice' => $practice,
            'practice_id' => $practice_id,
        ];

        return view('layouts.livewire', [
            'page_title' => $page_title,
            'has_back' => $has_back,
            'livewire_component' => $livewire_component,
            'livewire_data' => $livewire_data,
        ]);
    }

    public function deleteMedication(Practice $practice, $medicationId)
    {
        $medication = PracticeMedication::where('id', $medicationId)->where('practice_id', $practice->id)->first();

        if (!$medication) {
            return response()->json([
                'message' => __('messages.medication_not_found'),
                'status' => 0,
            ]);
        }
        $medication->delete();

        return response()->json([
            'message' => __('messages.medication_deleted_successfully'),
            'status' => 1,
        ]);
    }

    public function deleteToken(Practice $practice, $tokenId)
    {
        $token = PracticeToken::where('id', $tokenId)->where('practice_id', $practice->id)->first();

        if ($token) {
            list($tokenId) = explode('|', $token->token, 2);
            $accessToken = DB::table('personal_access_tokens')->where('id', $tokenId)->delete();
            $token->delete();
        } else {
            return response()->json([
                'message' => __('messages.token_not_found'),
                'status' => 0,
            ]);
        }

        return response()->json([
            'message' => __('messages.token_deleted_successfully'),
            'status' => 1,
        ]);
    }

}
