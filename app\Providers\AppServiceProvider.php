<?php

namespace App\Providers;

use Illuminate\Queue\Events\JobQueued;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Validator;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        Event::listen(JobQueued::class, function (JobQueued $event) {
              $payload = json_decode($event->payload, true);
            
            // Get the job instance
            $job = unserialize($payload['data']['command']);

            // Check if job has metadata
            if (property_exists($job, 'metadata') && !empty($job->metadata)) {
                // Update the job record immediately
                DB::table('jobs')
                    ->where('id', $event->id)
                    ->update([
                        'title' => $job->metadata['title'] ?? class_basename(get_class($job)),
                        'total' => $job->metadata['total'] ?? null,
                    ]);
            }
        });

        // Add custom Blade directive for storage URLs that automatically selects the right disk
        Blade::directive('storage_url', function ($expression) {
            return "<?php echo app('storage.url.helper')($expression); ?>";
        });

        // Add a helper function to get the appropriate storage URL based on environment
        app()->singleton('storage.url.helper', function () {
            return function ($path) {
                // Use S3 in production, public disk in local/development
                if (app()->environment('production')) {
                    return Storage::disk('s3')->url($path);
                } else {
                    return Storage::disk('public')->url($path);
                }
            };
        });

        Validator::includeUnvalidatedArrayKeys();
    }

    protected function getStorageUrl($path)
    {
        // Use S3 in production, public disk in local/development
        if (app()->environment('production')) {
            return Storage::disk('s3')->url($path);
        } else {
            return Storage::disk('public')->url($path);
        }
    }
}
