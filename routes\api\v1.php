<?php

use Illuminate\Support\Facades\Route;

Route::group(['middleware' => ['localization']], function () {

    Route::post('practice-create', 'v1\PracticeController@store');
    
    Route::post('destroy-patient', 'v1\UserController@destroyPatient');


    Route::group(['middleware' => ['auth:sanctum']], function () {
        Route::post('practice-details', 'v1\PracticeController@getPracticeByToken');

        Route::get('practice', 'v1\PracticeController@index');

        Route::get('medication', 'v1\MedicationController@index');
        Route::post('medication-create', 'v1\MedicationController@store');
        Route::put('medication-update/{medication}', 'v1\MedicationController@update');
        Route::delete('medication-delete/{medication}', 'v1\MedicationController@destroy');

        Route::get('provider', 'v1\UserController@indexUsers');

        Route::get('scripts', 'v1\ScriptController@index');
        Route::post('order/void', 'v1\VoidController@void');
        Route::post('order/create', 'v1\ScriptController@createImport');
        Route::get('scripts/generate-pdf/{importFileId}', 'v1\ScriptController@generatePdf');
        Route::post('scripts/return-script', 'v1\ScriptController@returnScript');

        Route::post('practice-generate-token', 'v1\PracticeController@generateToken');
        Route::post('practice-delete/{practice}', 'v1\PracticeController@delete');
    });
});
