<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('script_templates', function (Blueprint $table) {
            $table->dropColumn(['stregnth', 'dosing']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('script_templates', function (Blueprint $table) {
            $table->string('stregnth')->nullable();
            $table->string('dosing')->nullable();
        });
    }
};
