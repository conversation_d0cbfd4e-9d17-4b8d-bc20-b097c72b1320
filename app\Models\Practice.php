<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Laravel\Sanctum\HasApiTokens;

class Practice extends Model
{
    use HasFactory, HasApiTokens;

    //TABLE
    public $table = 'practices';

    //FILLABLES
    protected $fillable = [
        'name',
        'address',
        'city',
        'state',
        'zip',
        'phone',
        'fax',
        'default_order_method',
        'dispensepro_abbreviation',
        'api_token',
        'is_active',
    ];

    //HIDDEN
    protected $hidden = [];

    //APPENDS
    protected $appends = [];

    //WITH
    protected $with = [];

    //CASTS
    protected $casts = [];

    //RULES
    public static $getListRules = [];

    //RELATIONSHIPS
    public function providers()
    {
        return $this->hasMany(PracticeProvider::class, 'practice_id', 'id');
    }

    public function medications()
    {
        return $this->hasMany(Medication::class, 'practice_id');
    }

    public function practiceTokens()
    {
        return $this->hasMany(PracticeToken::class, 'practice_id');
    }

    //ATTRIBUTES
    //public function getExampleAttribute()
    //{
    //    return $data;
    //}
}