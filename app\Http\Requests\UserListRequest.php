<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UserListRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        //return {{model}}}::$getListRules;
        return [
            'transaction_id' => 'required|max:1024',
            'page' => 'nullable|integer|gt:0',
            'per_page' => 'nullable|integer|gt:0',
        ];
    }
}
