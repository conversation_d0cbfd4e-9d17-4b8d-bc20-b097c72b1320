<?php

namespace Database\Seeders;

use App\Models\ScriptTemplate;
use Illuminate\Database\Seeder;

class ScriptTemplateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        ScriptTemplate::insert([
            [
                'title' => 'SMG Starting Dose 0.25+0.5+1.0',
                'medication' => 'Semaglutide',
                'refills' => '0',
                'sig' => 'Inject 10 units(0.25mg) SubQ weekly for 4 weeks, then inject 20 units (0.5mg) SubQ weekly for 4 weeks, then inject 40 units (1.0mg) SubQ weekly ongoing',
                'vial_quantity' => '1',
                'notes' => 'NLF - [RX key]– pharmacy authorized to prescribe applicable syringes (#10). Billed to practice and shipped to the patient’s address.',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'title' => 'SMG Regular Dose 1.0',
                'medication' => 'Semaglutide',
                'refills' => '0',
                'sig' => 'Inject 40 units (1.0mg) SubQ  weekly ongoing',
                'vial_quantity' => '1',
                'notes' => 'NLF - [RX key]– pharmacy authorized to prescribe applicable syringes (#10). Billed to practice and shipped to the patient’s address.',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'title' => 'SMG Dose Increase 1.75+2+2.4',
                'medication' => 'Semaglutide',

                'refills' => '0',
                'sig' => 'Inject 70 units (1.75mg) subQ weekly for 4 weeks, then in.ject 80 units (2.0mg) subQ weekly for 4 weeks, then inject 96 units (2.4mg) SubQ weekly ongoing',
                'vial_quantity' => '2',
                'notes' => 'NLF - [RX key]– pharmacy authorized to prescribe applicable syringes (#10). Billed to practice and shipped to the patient’s address.',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'title' => 'SMG High Dose 2.4',
                'medication' => 'Semaglutide',

                'refills' => '0',
                'sig' => 'Inject 96 units (2.4mg) SubQ weekly ongoing',
                'vial_quantity' => '1',
                'notes' => 'NLF - [RX key]– pharmacy authorized to prescribe applicable syringes (#10). Billed to practice and shipped to the patient’s address.',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'title' => 'SMG Maintenance Dose 0.5',
                'medication' => 'Semaglutide',

                'refills' => '0',
                'sig' => 'Inject 20 units (0.5mg) SubQ weekly ongoing',
                'vial_quantity' => '1',
                'notes' => 'NLF - [RX key]– pharmacy authorized to prescribe applicable syringes (#10). Billed to practice and shipped to the patient’s address.',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'title' => 'TZP Starting Dose 2.5+5',
                'medication' => 'Tirzepatide',

                'refills' => '0',
                'sig' => 'Inject 25 units (2.5mg) subQ weekly for 4 weeks, Then Inject 50 units (5mg) subQ weekly ongoing',
                'vial_quantity' => '1',
                'notes' => 'NLF - [RX key]– pharmacy authorized to prescribe applicable syringes (#10). Billed to practice and shipped to the patient’s address.',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'title' => 'TZP Regular Dose 7.5',
                'medication' => 'Tirzepatide',

                'refills' => '0',
                'sig' => 'Inject 75 units (7.5mg) subQ weekly ongoing',
                'vial_quantity' => '2',
                'notes' => 'NLF - [RX key]– pharmacy authorized to prescribe applicable syringes (#10). Billed to practice and shipped to the patient’s address.',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'title' => 'TZP Dose Increase 10+12.5',
                'medication' => 'Tirzepatide',

                'refills' => '0',
                'sig' => 'Inject 100 units (10mg) subQ weekly for 4 weeks, Then Inject 125 units (12.5mg) subQ weekly ongoing',
                'vial_quantity' => '3',
                'notes' => 'NLF - [RX key]– pharmacy authorized to prescribe applicable syringes (#10). Billed to practice and shipped to the patient’s address.',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'title' => 'TZP High Dose 15',
                'medication' => 'Tirzepatide',

                'refills' => '0',
                'sig' => 'Inject 150 units (15mg) subQ weekly for 4 weeks',
                'vial_quantity' => '4',
                'notes' => 'NLF - [RX key]– pharmacy authorized to prescribe applicable syringes (#10). Billed to practice and shipped to the patient’s address.',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'title' => 'TZP Maintenance Dose 2.5',
                'medication' => 'Tirzepatide',

                'refills' => '0',
                'sig' => 'Inject 25 units (2.5mg) subQ weekly ongoing',
                'vial_quantity' => '1',
                'notes' => 'NLF - [RX key]– pharmacy authorized to prescribe applicable syringes (#10). Billed to practice and shipped to the patient’s address.',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
    }
}
