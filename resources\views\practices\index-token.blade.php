@extends('master')

@section('content')
@if (session()->has('success-message'))
<div class="text-success p-5 mb-3" role="alert">
    {{ session('success-message') }}
    <button type="button" class="close text-success" onclick="this.parentElement.remove()" aria-label="Close">
        <span aria-hidden="true">&times;</span>
    </button>
</div>    
@endif
    <div class="card card-custom mb-5">


        <div class="card-body" x-data="{ showFilter: false }">

            <div class="row justify-content-between ">
                <div class="col-12 col-sm-8 col-md-6 col-lg-4 col-xl-3 mb-6">
                    <div class="input-icon">
                        <button type="button" class="btn btn-primary" id="add-box-btn">
                            <i class="fa fa-plus"></i>
                            Generate Token
                        </button>
                    </div>
                </div>
            </div>

            <div class="datatable datatable-bordered datatable-head-custom" id="tokens_dt"></div>
        </div>
    </div>
@endsection
@section('styles')
@endsection
@section('scripts')
    <script src="https://cdn.jsdelivr.net/gh/alpinejs/alpine@v2.7.0/dist/alpine.min.js" defer></script>
    <script>

        var datatable;
        var datatableElement;
        var searchElement;
        var columnArray;

        const storagePath = `{{ url('/storage') }}`;
        let url = "{{ Storage::url('/') }}";
        const apiRoute = `{{ route('practices.api.tokens', ['practice' => $practice->id]) }}`;
        const deleteRoute = `{{ route('practices.delete-token', ['practice' => $practice->id, 'tokenId' => '::TOKEN']) }}`;

        datatableElement = $('#tokens_dt');
        searchElement = $('#tokens_search');

        columnArray = [
            {
                field: 'practice.name',
                title: `Practice Name`,
                width: '200',
                sortable: true,
            },
            {
                field: 'token',
                title: `Token`,
                width: '500',
                sortable: true,
                template: function(data) {
                    return `<div class="d-flex align-items-center">
                        <span class="mr-3">${data.token}</span>
                        <i class="far fa-clipboard copy-token" style="cursor:pointer; font-size: 18px;" data-token="${data.token}" title="Click to copy"></i>
                    </div>`;
                }
            },
            {
                field: 'created_at',
                title: `Created At`,
                width: 'auto',
                sortable: true,
                template: function(data) {
                    return data.created_at ? moment(data.created_at).format('MM/DD/YYYY hh:mm A') : '';
                }
            },
            {
                field: 'action',
                title: `Action`,
                width: 'auto',
                sortable: false,
                template: function(data) {
                    return `<a href="${deleteRoute.replace('::ID', {{ $practice->id }}).replace('::TOKEN', data.id)}" class="btn btn-sm btn-clean btn-icon delete-btn" data-toggle="tooltip" title="Delete Token" data-id="${data.id}" data-token="${data.id}">
                    <i class="menu-icon fas fa-trash"></i>
                    </a>`;
                }
            }
        ];

        datatable = datatableElement.KTDatatable({
            data: {
                type: 'remote',
                source: {
                    read: {
                        url: apiRoute.replace('::ID', '{{ $practice->id }}'),
                        headers: {
                            'X-CSRF-TOKEN': '{{ csrf_token() }}'
                        },
                        map: function (raw) {
                            var dataSet = raw;
                            if (typeof raw.data !== 'undefined') {
                                dataSet = raw.data;
                            }
                            return dataSet;
                        },
                    },
                },
                pageSize: 10,
                serverPaging: true,
                serverFiltering: true,
                serverSorting: true,
            },
            pagination: true,
            search: {
                input: searchElement,
                key: 'search'
            },
            layout: {
                customScrollbar: false,
                scroll: true,
            },
            columns: columnArray
        });

            $('#add-box-btn').on('click', function() {
            
                Swal.fire({
                    title: 'Are you sure?',
                    text: "Generate token?",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonText: 'Yes, generate token!'
                }).then((result) => {
                    if (result.isConfirmed) {
                $.ajax({
                    url: "{{ route('practices.generate-token', ['practice' => $practice->id]) }}",
                    type: "POST",
                    headers: {
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        toastr.success(response.message);
                        datatable.reload(); // Reload the datatable to show the new token
                    },
                    error: function(response) {
                        toastr.error(response.message);
                    }
                    });
                }
            });
        });

        $(document).on('click', '.delete-btn', function (e) {
            e.preventDefault();
            let id = $(this).data('id');
            let tokenId = $(this).data('token');
            Swal.fire({
                title: 'Are you sure?',
                text: 'Deleting this token will remove API access. Continue?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Yes, Delete!'
            }).then((result) => {
                if (result.isConfirmed) {
                    $.ajax({
                        url: deleteRoute.replace('::ID', id).replace('::TOKEN', tokenId),
                        type: 'POST',
                        headers: {
                            'X-CSRF-TOKEN': '{{ csrf_token() }}'
                        },
                        success: function(response) {
                            toastr.success(response.message);
                            datatable.reload();
                        },
                        error: function(response) {
                            toastr.error(response.message);
                        }
                    })
                }
            });
        });

        // Add copy-to-clipboard handler for token
        $(document).on('click', '.copy-token', function() {
            var token = $(this).data('token');
            if (navigator.clipboard) {
                navigator.clipboard.writeText(token).then(function() {
                    toastr.success('Token copied to clipboard!');
                }, function() {
                    toastr.error('Failed to copy token.');
                });
            } else {
                // Fallback for older browsers
                var $temp = $("<input>");
                $("body").append($temp);
                $temp.val(token).select();
                document.execCommand("copy");
                $temp.remove();
                toastr.success('Token copied to clipboard!');
            }
        });
    </script>
@endsection