<?php

namespace Database\Seeders;

use App\Models\Medication;
use Illuminate\Database\Seeder;

class MedicationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Initial medications with prefill data
        $medications = [
            [
                'name' => 'Semaglutide',
                'ndc' => '99999-5604-NN',
                'is_active' => 1,
                'vial_quantity' => '1',
                'days_supply' => '28',
                'sig' => 'Inject 40 units (1.0mg) SubQ weekly ongoing',
                'notes' => 'NLF - [RX key]– pharmacy authorized to prescribe applicable syringes (#10). Billed to practice and shipped to the patient\'s address.',
                'refills' => '1',
            ],
            [
                'name' => 'Tirzepatide',
                'ndc' => '99999-5604-NN',
                'is_active' => 1,
                'vial_quantity' => '2',
                'days_supply' => '28',
                'sig' => 'Inject 75 units (7.5mg) subQ weekly ongoing',
                'notes' => 'NLF - [RX key]– pharmacy authorized to prescribe applicable syringes (#10). Billed to practice and shipped to the patient\'s address.',
                'refills' => '1',
            ],
        ];

        foreach ($medications as $medication) {
            Medication::updateOrCreate(
                [
                    'name' => $medication['name'],
                    'ndc' => $medication['ndc'],
                ],
                $medication
            );
        }
    }
}
