<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PracticeProvider extends Model
{
    use HasFactory;

    //TABLE
    public $table = 'practice_providers';

    //FILLABLES
    protected $fillable = ['practice_id', 'provider_id'];

    //HIDDEN
    protected $hidden = [];

    //APPENDS
    protected $appends = [];

    //WITH
    protected $with = [];

    //CASTS
    protected $casts = [];

    //RULES
    public static $getListRules=[];

    //RELATIONSHIPS
    //public function example(){
    //    return $this->hasMany();
    //}

    public function practice()
    {
        return $this->belongsTo(Practice::class, 'practice_id', 'id');
    }

    public function user()
    {
        //provider_id == user(id) (id in users table)
        return $this->belongsTo(User::class, 'provider_id', 'id');
    }

    //ATTRIBUTES
    //public function getExampleAttribute()
    //{
    //    return $data;
    //}
}
