<?php

namespace App\Http\Livewire\Practice\Medications;

use App\Models\Medication;
use App\Models\Practice;
use App\Models\PracticeMedication;
use Livewire\Component;
use Illuminate\Support\Facades\Auth;

class CreateEdit extends Component
{
    public $medications = [];
    public $practice_id;
    public $medication_id; // <-- Add this

    protected $rules = [
        'medication_id' => 'required|exists:medications,id',
    ];

    public function messages()
    {
        return [
            'medication_id.required' => 'Medication is required',
            'medication_id.exists' => 'Selected medication does not exist',
        ];
    }

    public function mount()
    {
        $this->loadMedications();
    }

    public function loadMedications()
    {
        $this->medications = Medication::whereNotIn('id', function($query) {
            $query->select('medication_id')
                ->from('practice_medications')
                ->where('practice_id', $this->practice_id);
        })->get();
    }

    public function store()
    {
        $this->validate();

        // Check if the combination already exists
        $exists = PracticeMedication::where('practice_id', $this->practice_id)
            ->where('medication_id', $this->medication_id)
            ->exists();

        if ($exists) {
            session()->flash('error-message', 'This medication is already assigned to the practice.');
            return redirect()->route('practices.medications', $this->practice_id);
        }

        $practice_medication = PracticeMedication::create([
            'practice_id' => $this->practice_id,
            'medication_id' => $this->medication_id,
        ]);

        $practice_medication->save();

        if ($practice_medication) {

        session()->flash('success-message', 'Medication added successfully');
        }

        return redirect()->route('practices.medications', $this->practice_id);
    }

    public function render()
    {
        return view('livewire.practice.medications.create-edit');
    }
}
