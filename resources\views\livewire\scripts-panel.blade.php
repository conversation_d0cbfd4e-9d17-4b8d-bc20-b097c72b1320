<div wire:poll.1000ms="pollProgress">
    <div class="container">
        <div class="mt-3">
            @if($title)
            <h2 class="text-lg font-semibold text-gray-800">
                🛠️ Currently Processing: <span class="text-blue-600">{{ $title ?? 'No Active Job' }}</span>
            </h2>
            @endif
            <p class="text-sm text-gray-600 mt-1">
                Progress: <strong>{{ $processed }}</strong> of <strong>{{ $total }}</strong> tasks completed
            </p>
            <div class="progress">
                <div class="progress-bar" role="progressbar"
                    style="width: {{ ($total > 0) ? ($processed / $total) * 100 : 0 }}%;">
                    {{ intval(($processed / max($total, 1)) * 100) }}%
                </div>
            </div>
        </div>

        <table class="table table-bordered mt-5 p-5">
            <thead>
                <tr>
                    <td>Id</td>
                    <td>Title</td>
                    <td>Total</td>
                    <td>Status</td>
                </tr>
            </thead>
            <tbody>
                @forelse ($jobs as $job)
                <tr>
                    <td>{{ $loop->iteration }}</td>
                    <td>{{ $job->title }}</td>
                    <td>{{ $job->total }}</td>
                    <td>
                        @if ($loop->iteration === 1)
                        <span>🟢</span> RUNNING
                        @else
                        <span>🟡</span> Will start soon
                        @endif
                    </td>
                </tr>
                @empty
                <tr>
                    <td colspan="5" class="text-center">No queue found</td>
                </tr>
                @endforelse
            </tbody>
        </table>
        @if (session()->has('message'))
        <div class="alert alert-success mt-3">
            {{ session('message') }}
        </div>
        @endif
    </div>

</div>