<?php

namespace App\Http\Controllers;

use App\Models\ImportFile;
use App\Models\Medication;
use App\Models\State;
use App\Traits\FaxManager;
use App\Traits\DispenseProManager;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Webklex\PDFMerger\Facades\PDFMergerFacade as PDFMerger;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Exception;
use Illuminate\Support\Str;
use PhpOffice\PhpSpreadsheet\Calculation\Web;
use App\Models\WebHook;

class TestController extends Controller
{
    use FaxManager, DispenseProManager;



    public function mergePdf(Request $request)
    {
        $import_files = ImportFile::with('import')
            ->where('status', ImportFile::STATUS_PENDING_APPROVAL)
            // ->whereIn('id', $this->import_file_ids)
            ->get();

        $filePaths = [];
        foreach ($import_files as $import_file) {

            $file = $import_file->file_path;
            if (Storage::exists($file)) {
                $filePaths[] = $file;
            }
        }

        $outputDirectory = 'temp/merged_pdfs';

        // try {
        // Initialize the PDF merger
        $oMerger = PDFMerger::init();

        // Create a unique filename for the merged PDF
        $outputFilename = Str::random(20) . '.pdf';

        // Ensure the output directory exists
        if (!Storage::exists($outputDirectory)) {
            Storage::makeDirectory($outputDirectory);
        }

        $outputPath = "{$outputDirectory}/{$outputFilename}";
        $validPdfsCount = 0;

        // Process each file path
        foreach ($filePaths as $filePath) {
            // Normalize the file path to handle mixed slashes and absolute paths

            // $normalizedPath = $this->normalizeStoragePath($filePath);

            $normalizedPath = $filePath;

            // Skip if file doesn't exist
            if (!Storage::exists($normalizedPath)) {
                return "no path" . $normalizedPath;
                continue;
            }

            $normalizedPath = Storage::path($normalizedPath);
            // Get file extension and check if it's a PDF
            $extension = pathinfo($filePath, PATHINFO_EXTENSION);
            if (strtolower($extension) !== 'pdf') {
                return "no extension";
                continue;
            }
            // Get the full path to the file in storage
            $fullPath = $normalizedPath;

            // Add the PDF to the merger
            $oMerger->addPDF($fullPath, 'all');
            $validPdfsCount++;
        }

        // If no valid PDFs were found, return null
        if ($validPdfsCount === 0) {

            return " no pdfs";
        }

        // Merge the PDFs
        $oMerger->merge();

        // Save the merged PDF
        $fullOutputPath = Storage::path($outputPath);
        $oMerger->save($fullOutputPath);
        return $fullOutputPath;

        // Check if the merged PDF was created successfully
        if (!Storage::exists($outputPath)) {
            return null;
        }

        return [
            'success' => true,
            'path' => $outputPath,
            'full_path' => $fullOutputPath,
            'filename' => $outputFilename,
            'merged_count' => $validPdfsCount
        ];
        // } catch (Exception $e) {

        //     Log::error('Failed to merge PDFs', [
        //         'error' => $e->getMessage(),
        //         'file' => $e->getFile(),
        //         'line' => $e->getLine()
        //     ]);
        //     return null;
        // }
    }
    public function test(Request $request)
    {
        // Check if file exists in the request
        if (!$request->hasFile('fax_file')) {
            return response()->json([
                'success' => '0',
                'message' => 'No file uploaded. Please upload a file with the fax_file parameter.'
            ], 400);
        }

        $file = $request->file('fax_file');

        // Call the uploadFiles method with the file - use static method
        $response = FaxManager::uploadFiles($file);

        return response()->json([
            'success' => '1',
            'response' => $response,
        ]);
    }

    public function sendTest(Request $request)
    {
        if (!$request->hasFile('fax_file')) {
            return response()->json([
                'success' => '0',
                'message' => 'No file uploaded. Please upload file(s) with the fax_file parameter.'
            ], 400);
        }

        $filesArray = [];

        $uploadedFiles = $request->file('fax_file');

        // Handle both single and multiple uploads
        if (!is_array($uploadedFiles)) {
            $uploadedFiles = [$uploadedFiles];
        }

        foreach ($uploadedFiles as $file) {
            // Upload each file and collect the path
            $response = FaxManager::uploadFiles($file);
            if (isset($response['path'])) {
                $filesArray[] = $response['path'];
            }
        }

        // Get user_id from request or use default
        $userId = $request->input('user_id', config('fax.user_id'));
        $to = $request->input('to', config('fax.to_number'));
        $from = $request->input('from', config('fax.from_number'));

        // Now $filesArray will have all file paths
        $sendFax = FaxManager::sendFax($to, $filesArray, $from, $userId);

        return response()->json([
            'success' => '1',
            'message' => 'Fax sent successfully',
            'response' => $sendFax,
        ]);
    }

    public function testDispensePro(Request $request)
    {
        $externalOrderId = $request?->orders[0]['externalOrderId'] ?? null;
        if ($request?->orders &&  $externalOrderId) {
            $import_file = ImportFile::where('order_id', $request->orders[0]['externalOrderId'])->first();
            if ($import_file) {
                $data = Webhook::create([
                    'data' => json_encode($request->all()),
                    'ip_address' => $request->ip(),
                    'import_file_id' => $import_file->id,
                    'order_id' => $request->orders[0]['externalOrderId']
                ]);
            }
        } else {
            $data = Webhook::create([
                'data' => json_encode($request->all()),
                'ip_address' => $request->ip(),
            ]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Webhook received',
        ], 200);

        // try {

        //     $import_file = ImportFile::where('id', $id)->with('import')->first();
        //     $customer_dtls = $import_file->import->user;
        //     $stateName = State::where('short_name', $import_file->state)->value('name');
        //     $ndc = Medication::where('name', $import_file->medication)->value('ndc');

        //     $filePath = $import_file->value('file_path');
        //     $orderId = strtoupper(Str::random(3)) . rand(10000, 99999); // e.g., "XJF45783"

        //     $pdf = Storage::get($filePath);

        //     if ($pdf) {
        //         $pdf = base64_encode($pdf);
        //     } else {
        //         throw new Exception("PDF file not found or empty.");
        //     }

        //     $data = [
        //         "action" => "order",
        //         "orderId" => "$orderId", // random number generated 
        //         // "shipTo" => "patient",
        //         // "billTo" => "patient",
        //         "privacyConsent" => "false", // Send SMS & Email tracking details
        //         "patient" => [
        //             // "id" => "**********", // customer patient id
        //             // "lastName" => $import_file->last_name,
        //             // "firstName" => $import_file->first_name,
        //             "lastName" =>"MAXLIFE",
        //             "firstName" => "TEST",
        //             "address" => $import_file->address,
        //             "address2" => "",
        //             "city" => $import_file->city,
        //             "region" => $stateName,
        //             "postalCode" => $import_file->zip,
        //             "countryCode" => '',
        //             "dob" => Carbon::parse($import_file->dob)->format('Ymd'),
        //             "gender" => $import_file->gender,
        //             "phone" => $import_file->phone,
        //         ],
        //         "customer" => [
        //             "abbreviation" => $customer_dtls->dispense_abbreviation, // Assign customer to order
        //             "name" => $customer_dtls->printed_name,
        //             "address" => $customer_dtls->address,
        //             "address2" => "",
        //             "city" => $customer_dtls->city,
        //             "region" => $customer_dtls->state->name,
        //             "postalCode" => $customer_dtls->zip,
        //             "phone" => $customer_dtls->phone
        //         ],
        //         "hasPrescription" => "true", // Required
        //         "rxs" => [
        //             [
        //                 "description" => $import_file->notes,
        //                 "dose" => $import_file->dosing,
        //                 "quantity" => $import_file->vial_quantity,
        //                 "units" => "each",
        //                 "ndc" => $ndc, // NDC of medication or Compound CSN
        //                 "daysSupply" => 28,
        //                 "sig" => $import_file->sig,
        //                 "writtenDate" => $import_file->script_date,
        //                 "prescriberNPI" => **********, // required for each Rx
        //                 "refills" => $import_file->refills,
        //             ]
        //         ],
        //         "pdfScript" => $pdf // Base64 encoded prescription PDF
        //     ];

        //     $response = DispenseProManager::DispenseProCreateOrder($data);

        //     if ($response) {
        //         $import_file->status = ImportFile::STATUS_SENT;
        //         $import_file->sent_at = Carbon::now();
        //         $import_file->save();
        //         $message = "Order created successfully in Dispense Pro";
        //     } else {
        //         $message = "Failed to create order in Dispense Pro";
        //     }

        //     session()->flash('success-message', $message);

        //     return back();
        // } catch (Exception $e) {

        //     session()->flash('error-message', $e->getMessage());
        //     return back();
        // }
    }

    public function testDispenseProProviders(Request $request)
    {
        try {
            $response = DispenseProManager::GetProviders();
            return response()->json([
                'success' => true,
                'data' => $response->body(),
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    public function void(Request $request)
    {

        $validated = $request->validate([
            'orderId'   => ['required'],
            'orderId.*' => 'string',
        ]);

        // Allow both single string and array input
        $orderIds = $validated['orderId'];

        $results = self::DispenseProVoidOrder($orderIds);

        return response()->json($results);
    }
}
