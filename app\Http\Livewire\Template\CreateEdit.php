<?php

namespace App\Http\Livewire\Template;

use App\Models\Medication;
use App\Models\ScriptTemplate;
use Livewire\Component;

class CreateEdit extends Component
{
    public $medications = [];
    public ScriptTemplate $script_template;

    public function mount(ScriptTemplate $script_template)
    {
        $this->medications = Medication::where('is_active', true)->get();
        $this->script_template = $script_template;
    }

    public function rules()
    {
        return [
            'script_template.title' => 'required|string|max:255',
            'script_template.medication' => 'nullable|string|max:255',
            'script_template.vial_quantity' => 'nullable|string|max:50',
            'script_template.sig' => 'nullable|string|max:500',
            'script_template.refills' => 'nullable|string|max:50',
            'script_template.notes' => 'nullable|string',
            'script_template.days_supply' => 'nullable|string|max:50',
        ];
    }


    public function saveTemplate()
    {
        $this->validate();

        if ($this->script_template->id) {
            $message = __('messages.script_template_updated');
        } else {
            $message = __('messages.script_template_saved');
        }

        $this->script_template->save();

        session()->flash('success-message', $message);

        return redirect()->route('scripts.template');
    }

    public function render()
    {
        return view('scripts.admin.template.create');
    }
}
