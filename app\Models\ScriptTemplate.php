<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ScriptTemplate extends Model
{
    use HasFactory;

    //TABLE
    public $table = 'script_templates';

    //FILLABLES
    protected $fillable = [
        'title',
        'medication',
        'vial_quantity',
        'refills',
        'sig',
        'notes',
        'days_supply',
    ];

    //HIDDEN
    protected $hidden = [];

    //APPENDS
    protected $appends = [];

    //WITH
    protected $with = [];

    //CASTS
    protected $casts = [];

    //RULES
    public static $getListRules = [];

    public function medication()
    {
        return $this->belongsTo(Medication::class, 'medication', 'name');
    }

    //RELATIONSHIPS
    //public function example(){
    //    return $this->hasMany();
    //}

    //ATTRIBUTES
    //public function getExampleAttribute()
    //{
    //    return $data;
    //}
}
