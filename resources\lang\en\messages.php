<?php

return [

    //user related messages
    'welcome' => 'Welcome to the application!',
    'dashboard' => 'Dashboard',
    'login_success' => 'You have successfully logged in.',
    'logout_success' => 'You have successfully logged out.',
    'User_deleted' => 'User deleted successfully.',
    'User_created' => 'User created successfully.',
    'User_updated' => 'User updated successfully.',
    'User_not_found' => 'User not found.',
    'User_already_exists' => 'User already exists.',
    'Admin_already_exists' => 'Admin already exists.',
    'password_changed_successfully' => 'Password changed successfully. Please log in with your new password.',
    'fax_queued_dispatch' => "Fax queued for dispatch. Status changed to 'Pending Dispatch' until delivery is confirmed.",
    'column_order_mismatch' => 'Column order mismatch: ',
    'no_data_beyond_row2' => 'No data was found beyond row 2 in the Excel file.',
    'user_created_successfully' => 'User created successfully. Login credentials have been sent to :email.',
    'script_not_found' => '<PERSON><PERSON>t not found.',
    'no_script_found_to_download' => 'No Script found to download',
    'zip_create_error' => 'Unable to create ZIP file. Please try again later.',
    'user_created_successfully_but_email_failed' => 'User created successfully, but the notification email could not be sent. Please provide the temporary password separately: :password',
    'user_updated_successfully_with_temp_password' => 'User updated successfully. A temporary password has been sent to the new email address.',
    'user_updated_successfully_but_email_failed' => 'User updated successfully, but the notification email could not be sent. Please provide the temporary password separately: :password',
    'user_updated_successfully' => 'User updated successfully.',
    'script_saved_successfully' => 'Script saved successfully!',
    'script_template_saved' => 'Script template saved successfully!',
    'script_template_updated' => 'Script template updated successfully!',

    'script_signed_sent' => 'Scripts signed and sent for approval successfully.',
    'script_sent_for_approval' => 'Script have been sent for approval.',
    'script_deleted' => 'Script deleted successfully.',
    'script_delete_error' => 'Error deleting script: ',
    'script_return_to_provider' => 'Script returned successfully.',
    'script_recalled_for_revision' => 'Script recalled successfully.',
    'script_recall_error' => 'An error occurred while recalling the script.',
    'import_record_not_found' => 'Import record could not be found.',
    'script_not_signed' => 'Script is not signed or the PDF file is missing.',
    'pdf_script_not_found' => 'PDF file not found.',
    'excel_upload_error' => 'Please upload an Excel file to continue.',
    'no_script_found_for_selected' => "No scripts found for this import with the selected status.",
    'import_id_not_found' => 'Unable to identify the import ID from the displayed records.',
    'script_not_found_for_import' => 'No script found for the specified import ID.',
    'prescription_status_update' => ':count prescriptions updated to :status. :processed PDFs regenerated.',
    'prescription_update' => ':count prescriptions updated to :status',
    'error_updating_status' => 'Error updating status: ',
    'no_record_id_provided' => 'No record ID provided',
    'error_retrieving_import_id' => 'Error retrieving import ID: ',
    'no_script_selected' => 'No Script selected',
    'no_script_found' => 'No script found',

    'device_time_not_provided' => 'Device time not provided.',
    'device_time_stored_successfully' => 'Device time stored successfully.',
    'processing_stopped' => 'Processing stopped at row :row. More than 5 required fields are blank in this row. Please ensure your data is complete.',
    'import_id_required' => 'Import ID is required.',
    'import_not_found' => 'Import not found.',
    'provider_not_found' => 'Provider not found for this import.',
    'excel_file_upload_prompt' => 'Please upload an Excel file and select a provider to continue.',
    'selected_provider_not_found' => 'Selected provider not found. Please select a valid provider.',
    'import_success' => 'Successfully imported :count prescriptions for :name. The scripts are now available in the provider\'s Ready to Sign section.',


    //fax messages
    'fax_message_sent' => 'Fax sent successfully.',
    'fax_sent_failed' => 'Unable to send fax. Please try again later.',
    'fax_api_error' => 'Fax API Error: ',
    'new_fax_number_field_added' => 'New fax number field added. Fill it in and click Save to store.',
    'fax_number_field_removed' => 'Fax number field removed.',
    'fax_number_deletion_error' => 'Cannot delete empty fax number. Please enter a valid number or remove the field.',
    'fax_number_deleted_successfully' => 'Fax number deleted successfully.',
    'duplicate_fax_numbers' => 'Duplicate fax numbers are not allowed.',
    'fax_saved' => 'Fax numbers saved successfully. :count fax number(s) processed.',
    'fax_validation_errors' => 'Please fix the validation errors and try again.',
    'error_saving_fax_numbers' => 'Error saving fax numbers: :error',
    'error_uploading_signature' => 'Error uploading signature: :error',


    //authorization
    'delete_script_unauthorized' => 'Unauthorized. Only administrators can delete scripts.',
    'delete_script_error' => 'Cannot delete script. Only scripts with status "New" or "Pending Approval" can be deleted.',

    //medication
    'medication_created' => 'Medication created successfully.',
    'medication_updated' => 'Medication updated successfully.',
    'medication_deleted' => 'Medication deleted successfully.',
    'medication_deleted_successfully' => 'Medication deleted successfully.',
    'medication_not_found' => 'Medication not found.',

    //void script
    'void_job_queued' => 'Void job has been queued.',
    'void_job_failed' => 'Void job failed.',
    'void_job_completed' => 'Void job completed.',
    'void_job_reverted' => 'Void job reverted.',
    'void_job_reverted_failed' => 'Void job reverted failed.',
    'void_job_reverted_completed' => 'Void job reverted completed.',
    'no_order_ids_to_void' => 'No order IDs to void.',

    //patient
    'patient_created' => 'Patient created successfully.',
    'patient_updated' => 'Patient updated successfully.',
    'patient_deleted' => 'Patient deleted successfully.',
    'practice_activated' => 'Practice activated successfully.',
    'practice_deactivated' => 'Practice deactivated successfully.',

    //Practice
    'practice_created' => 'Practice created successfully.',
    'practice_updated' => 'Practice updated successfully.',
    'practice_deleted' => 'Practice deleted successfully.',
    'token_deleted_successfully' => 'Token deleted successfully.',
    'token_not_found' => 'Token not found.',
    
    'provider_deleted_successfully' => 'Provider deleted successfully.',
];
