<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('default_dispatch_method')
                ->nullable()
                ->after('signature');

            $table->string('dispense_abbreviation')
                ->nullable()
                ->after('default_dispatch_method');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('default_dispatch_method');
            $table->dropColumn('dispense_abbreviation');
        });
    }
};
