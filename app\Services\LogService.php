<?php

namespace App\Services;

use App\Models\SystemLog;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Request;

/**
 * LogService - Enhanced with Change Tracking
 *
 * This service now supports tracking old and new values for edit operations.
 *
 * Usage Examples:
 *
 * 1. Manual tracking:
 *    $oldValues = LogService::getModelAttributes($user->getOriginal(), ['first_name', 'email']);
 *    $user->save();
 *    $newValues = LogService::getModelAttributes($user, ['first_name', 'email']);
 *    LogService::logProviderEdited($user, $oldValues, $newValues);
 *
 * 2. Using helper method:
 *    $result = LogService::trackModelChanges($user, ['first_name', 'email'], function() use ($user) {
 *        return $user->save();
 *    });
 *    LogService::logProviderEdited($user, $result['original'], $result['new']);
 */
class LogService
{
    /**
     * Log a user action
     */
    public static function logUserAction(string $type, string $message, array $context = [], ?User $user = null)
    {
        $user = $user ?? Auth::user();

        self::createLog([
            'type' => $type,
            'message' => $message,
            'context' => $context,
            'user' => $user,
        ]);
    }

    /**
     * Log a system error
     */
    public static function logSystemError(string $message, array $context = [])
    {
        self::createLog([
            'type' => SystemLog::TYPE_SYSTEM_ERROR,
            'message' => $message,
            'context' => $context,
        ]);
    }

    /**
     * Log user login
     */
    public static function logLogin(User $user)
    {
        self::createLog([
            'type' => SystemLog::TYPE_LOGIN,
            'message' => "User logged in",
            'user' => $user,
        ]);
    }

    /**
     * Log user logout
     */
    public static function logLogout(User $user)
    {
        self::createLog([
            'type' => SystemLog::TYPE_LOGOUT,
            'message' => "User logged out",
            'user' => $user,
        ]);
    }

    /**
     * Log script creation
     */
    public static function logScriptCreated(array $scriptData, ?User $user = null)
    {
        $user = $user ?? Auth::user();

        self::createLog([
            'type' => SystemLog::TYPE_SCRIPT_CREATED,
            'message' => "Script created for {$scriptData['patient_name']}",
            'context' => $scriptData,
            'user' => $user,
        ]);
    }

    /**
     * Log script editing
     */
    public static function logScriptEdited(array $scriptData, array $oldValues = [], array $newValues = [], ?User $user = null)
    {
        $user = $user ?? Auth::user();

        $context = $scriptData;

        // Add old and new values if provided
        if (!empty($oldValues) || !empty($newValues)) {
            $context['changes'] = self::formatChanges($oldValues, $newValues);
        }

        self::createLog([
            'type' => SystemLog::TYPE_SCRIPT_EDITED,
            'message' => "Script edited for {$scriptData['patient_name']}",
            'context' => $context,
            'user' => $user,
        ]);
    }

    /**
     * Log script signing
     */
    public static function logScriptSigned(array $scriptData, ?User $user = null)
    {
        $user = $user ?? Auth::user();

        self::createLog([
            'type' => SystemLog::TYPE_SCRIPT_SIGNED,
            'message' => "Script signed for {$scriptData['patient_name']}",
            'context' => $scriptData,
            'user' => $user,
        ]);
    }

    /**
     * Log script sending
     */
    public static function logScriptSent(array $scriptData, ?User $user = null)
    {
        $user = $user ?? Auth::user();

        self::createLog([
            'type' => SystemLog::TYPE_SCRIPT_SENT,
            'message' => "Script sent for {$scriptData['patient_name']}",
            'context' => $scriptData,
            'user' => $user,
        ]);
    }

    /**
     * Log provider creation
     */
    public static function logProviderCreated(User $provider, ?User $user = null)
    {
        $user = $user ?? Auth::user();

        self::createLog([
            'type' => SystemLog::TYPE_PROVIDER_CREATED,
            'message' => "Provider created: {$provider->first_name} {$provider->last_name}",
            'context' => ['provider_id' => $provider->id, 'provider_email' => $provider->email],
            'user' => $user,
        ]);
    }

    /**
     * Log provider editing
     */
    public static function logProviderEdited(User $provider, array $oldValues = [], array $newValues = [], ?User $user = null)
    {
        $user = $user ?? Auth::user();

        $context = [
            'provider_id' => $provider->id,
            'provider_email' => $provider->email
        ];

        // Add old and new values if provided
        if (!empty($oldValues) || !empty($newValues)) {
            $context['changes'] = self::formatChanges($oldValues, $newValues);
        }

        self::createLog([
            'type' => SystemLog::TYPE_PROVIDER_EDITED,
            'message' => "Provider edited: {$provider->first_name} {$provider->last_name}",
            'context' => $context,
            'user' => $user,
        ]);
    }

    /**
     * Log provider deactivation
     */
    public static function logProviderDeactivated(User $provider, ?User $user = null)
    {
        $user = $user ?? Auth::user();

        self::createLog([
            'type' => SystemLog::TYPE_PROVIDER_DEACTIVATED,
            'message' => "Provider deactivated: {$provider->first_name} {$provider->last_name}",
            'context' => ['provider_id' => $provider->id, 'provider_email' => $provider->email],
            'user' => $user,
        ]);
    }

    /**
     * Log provider activation
     */
    public static function logProviderActivated(User $provider, ?User $user = null)
    {
        $user = $user ?? Auth::user();

        self::createLog([
            'type' => SystemLog::TYPE_PROVIDER_ACTIVATED,
            'message' => "Provider activated: {$provider->first_name} {$provider->last_name}",
            'context' => ['provider_id' => $provider->id, 'provider_email' => $provider->email],
            'user' => $user,
        ]);
    }

    /**
     * Log staff creation
     */
    public static function logStaffCreated(User $staff, ?User $user = null)
    {
        $user = $user ?? Auth::user();

        self::createLog([
            'type' => SystemLog::TYPE_STAFF_CREATED,
            'message' => "Staff created: {$staff->first_name} {$staff->last_name}",
            'context' => ['staff_id' => $staff->id, 'staff_email' => $staff->email, 'role' => $staff->role],
            'user' => $user,
        ]);
    }

    /**
     * Log staff editing
     */
    public static function logStaffEdited(User $staff, array $oldValues = [], array $newValues = [], ?User $user = null)
    {
        $user = $user ?? Auth::user();

        $context = [
            'staff_id' => $staff->id,
            'staff_email' => $staff->email,
            'role' => $staff->role
        ];

        // Add old and new values if provided
        if (!empty($oldValues) || !empty($newValues)) {
            $context['changes'] = self::formatChanges($oldValues, $newValues);
        }

        self::createLog([
            'type' => SystemLog::TYPE_STAFF_EDITED,
            'message' => "Staff edited: {$staff->first_name} {$staff->last_name}",
            'context' => $context,
            'user' => $user,
        ]);
    }

    /**
     * Log staff deactivation
     */
    public static function logStaffDeactivated(User $staff, ?User $user = null)
    {
        $user = $user ?? Auth::user();

        self::createLog([
            'type' => SystemLog::TYPE_STAFF_DEACTIVATED,
            'message' => "Staff deactivated: {$staff->first_name} {$staff->last_name}",
            'context' => ['staff_id' => $staff->id, 'staff_email' => $staff->email, 'role' => $staff->role],
            'user' => $user,
        ]);
    }

    /**
     * Log staff activation
     */
    public static function logStaffActivated(User $staff, ?User $user = null)
    {
        $user = $user ?? Auth::user();

        self::createLog([
            'type' => SystemLog::TYPE_STAFF_ACTIVATED,
            'message' => "Staff activated: {$staff->first_name} {$staff->last_name}",
            'context' => ['staff_id' => $staff->id, 'staff_email' => $staff->email, 'role' => $staff->role],
            'user' => $user,
        ]);
    }

    /**
     * Log staff deletion
     */
    public static function logStaffDeleted(User $staff, ?User $user = null)
    {
        $user = $user ?? Auth::user();

        self::createLog([
            'type' => SystemLog::TYPE_STAFF_DELETED,
            'message' => "Staff deleted: {$staff->first_name} {$staff->last_name}",
            'context' => ['staff_id' => $staff->id, 'staff_email' => $staff->email],
            'user' => $user,
        ]);
    }

    /**
     * Log password change
     */
    public static function logPasswordChanged(?User $user = null)
    {
        $user = $user ?? Auth::user();

        self::createLog([
            'type' => SystemLog::TYPE_PASSWORD_CHANGED,
            'message' => "Password changed",
            'user' => $user,
        ]);
    }

    /**
     * Log temporary password sent
     */
    public static function logTempPasswordSent(User $targetUser, ?User $user = null)
    {
        $user = $user ?? Auth::user();

        self::createLog([
            'type' => SystemLog::TYPE_TEMP_PASSWORD_SENT,
            'message' => "Temporary password sent to {$targetUser->first_name} {$targetUser->last_name}",
            'context' => ['target_user_id' => $targetUser->id, 'target_user_email' => $targetUser->email],
            'user' => $user,
        ]);
    }

    /**
     * Log settings update
     */
    public static function logSettingsUpdated(string $settingType, array $context = [], ?User $user = null)
    {
        $user = $user ?? Auth::user();

        self::createLog([
            'type' => SystemLog::TYPE_SETTINGS_UPDATED,
            'message' => "Settings updated: {$settingType}",
            'context' => $context,
            'user' => $user,
        ]);
    }

    /**
     * Log Excel file import
     */
    public static function logExcelImported(string $fileName, int $recordCount, array $context = [], ?User $user = null)
    {
        $user = $user ?? Auth::user();

        self::createLog([
            'type' => SystemLog::TYPE_EXCEL_IMPORTED,
            'message' => "Excel file imported: {$fileName} ({$recordCount} records)",
            'context' => array_merge($context, ['file_name' => $fileName, 'record_count' => $recordCount]),
            'user' => $user,
        ]);
    }

    /**
     * Log script return
     */
    public static function logScriptReturned(array $scriptData, string $reason = '', ?User $user = null)
    {
        $user = $user ?? Auth::user();

        self::createLog([
            'type' => SystemLog::TYPE_SCRIPT_RETURNED,
            'message' => "Script returned for {$scriptData['patient_name']}" . ($reason ? " - Reason: {$reason}" : ''),
            'context' => array_merge($scriptData, ['return_reason' => $reason]),
            'user' => $user,
        ]);
    }

    /**
     * Log provider deletion
     */
    public static function logProviderDeleted(User $provider, ?User $user = null)
    {
        $user = $user ?? Auth::user();

        self::createLog([
            'type' => SystemLog::TYPE_PROVIDER_DELETED,
            'message' => "Provider deleted: {$provider->first_name} {$provider->last_name}",
            'context' => ['provider_id' => $provider->id, 'provider_email' => $provider->email],
            'user' => $user,
        ]);
    }

    /**
     * Log fax number addition
     */
    public static function logFaxAdded(string $faxNumber, string $label, ?User $user = null)
    {
        $user = $user ?? Auth::user();

        self::createLog([
            'type' => SystemLog::TYPE_FAX_ADDED,
            'message' => "Fax number added: {$faxNumber} ({$label})",
            'context' => ['fax_number' => $faxNumber, 'label' => $label],
            'user' => $user,
        ]);
    }

    /**
     * Log fax number update
     */
    public static function logFaxUpdated(string $faxNumber, string $label, array $changes = [], ?User $user = null)
    {
        $user = $user ?? Auth::user();

        self::createLog([
            'type' => SystemLog::TYPE_FAX_UPDATED,
            'message' => "Fax number updated: {$faxNumber} ({$label})",
            'context' => array_merge(['fax_number' => $faxNumber, 'label' => $label], $changes),
            'user' => $user,
        ]);
    }

    /**
     * Log fax number deletion
     */
    public static function logFaxDeleted(string $faxNumber, string $label, ?User $user = null)
    {
        $user = $user ?? Auth::user();

        self::createLog([
            'type' => SystemLog::TYPE_FAX_DELETED,
            'message' => "Fax number deleted: {$faxNumber} ({$label})",
            'context' => ['fax_number' => $faxNumber, 'label' => $label],
            'user' => $user,
        ]);
    }

    /**
     * Log fax sending
     */
    public static function logFaxSent(array $scriptData, string $faxNumber, array $context = [], ?User $user = null)
    {
        $user = $user ?? Auth::user();

        self::createLog([
            'type' => SystemLog::TYPE_FAX_SENT,
            'message' => "Fax sent for {$scriptData['patient_name']} to {$faxNumber}",
            'context' => array_merge($scriptData, ['fax_number' => $faxNumber], $context),
            'user' => $user,
        ]);
    }

    /**
     * Log fax sending failure
     */
    public static function logFaxFailed(string $errorMessage, string $reason = '',  ?User $user = null)
    {
        $user = $user ?? Auth::user();

        self::createLog([
            'type' => defined('SystemLog::TYPE_FAX_FAILED') ? SystemLog::TYPE_FAX_FAILED : 'fax_failed',
            'message' => "Fax send failed: $errorMessage",
            'context' => ['reason' => $reason],
            'user' => $user,
        ]);
    }

    public static function logDispenseProOrderCreated(array $orderData, ?User $user = null)
    {
        $user = $user ?? Auth::user();

        self::createLog([
            'type' => SystemLog::TYPE_DISPENSE_PRO_ORDER_CREATED,
            'message' => "DispensePro order created successfully",
            'context' => $orderData,
            'user' => $user,
        ]);
    }

    public static function logDispenseProOrderFailed(string $errorMessage, array $reason = [], ?User $user = null)
    {
        $user = $user ?? Auth::user();

        self::createLog([
            'type' => SystemLog::TYPE_DISPENSE_PRO_ORDER_FAILED,
            'message' => "DispensePro order failed: $errorMessage",
            'context' => ['reason' => $reason],
            'user' => $user,
        ]);
    }

    /**
     * Log script download
     */
    public static function logScriptDownloaded(array $scriptData, string $downloadType = 'single', ?User $user = null)
    {
        $user = $user ?? Auth::user();

        self::createLog([
            'type' => SystemLog::TYPE_SCRIPT_DOWNLOADED,
            'message' => "Script downloaded for {$scriptData['patient_name']} ({$downloadType})",
            'context' => array_merge($scriptData, ['download_type' => $downloadType]),
            'user' => $user,
        ]);
    }

    /**
     * Log script deletion
     */
    public static function logScriptDeleted(array $scriptData, ?User $user = null)
    {
        $user = $user ?? Auth::user();

        self::createLog([
            'type' => SystemLog::TYPE_SCRIPT_DELETED,
            'message' => "Script deleted for {$scriptData['patient_name']}",
            'context' => $scriptData,
            'user' => $user,
        ]);
    }

    /**
     * Log NDC not found
     */
    public static function NdcNotFound(string $medication, int $importFileId, ?User $user = null)
    {
        $user = $user ?? Auth::user();

        self::createLog([
            'type' => SystemLog::TYPE_NDC_NOT_FOUND,
            'message' => "NDC not found for medication: {$medication} in import file ID: {$importFileId}",
            'context' => ['medication' => $medication, 'import_file_id' => $importFileId],
            'user' => $user,
        ]);
    }
    /**
     * Log state not found
     */
    public static function StateNotFound(string $stateCode, int $importFileId, ?User $user = null)
    {
        $user = $user ?? Auth::user();

        self::createLog([
            'type' => SystemLog::TYPE_STATE_NOT_FOUND,
            'message' => "State not found for state code: {$stateCode} in import file ID: {$importFileId}",
            'context' => ['state_code' => $stateCode, 'import_file_id' => $importFileId],
            'user' => $user,
        ]);
    }

    public static function logPdfNotFound(int $importFileId, ?User $user = null)
    {
        $user = $user ?? Auth::user();

        self::createLog([
            'type' => SystemLog::TYPE_PDF_NOT_FOUND,
            'message' => "PDF file not found for import file ID: {$importFileId}",
            'context' => ['import_file_id' => $importFileId],
        ]);
    }


    /**
     * Create a log entry
     */
    private static function createLog(array $data)
    {
        $user = $data['user'] ?? null;

        // dd($user);
        
        SystemLog::create([
            'timestamp' => now(),
            'type' => $data['type'],
            'user_type' => $user ? $user->role : null,
            'username' => $user ? "{$user->first_name} {$user->last_name}" : null,
            'user_id' => $user ? $user->id : null,
            'message' => $data['message'],
            'context' => $data['context'] ?? null,
            'ip_address' => Request::ip(),
            'user_agent' => Request::userAgent(),
        ]);
    }

    /**
     * Format changes for logging
     */
    private static function formatChanges(array $oldValues, array $newValues): array
    {
        $changes = [];

        // Get all unique keys from both arrays
        $allKeys = array_unique(array_merge(array_keys($oldValues), array_keys($newValues)));

        foreach ($allKeys as $key) {
            $oldValue = $oldValues[$key] ?? null;
            $newValue = $newValues[$key] ?? null;

            // Only log if values are different
            if ($oldValue !== $newValue) {
                $changes[$key] = [
                    'old' => $oldValue,
                    'new' => $newValue
                ];
            }
        }

        return $changes;
    }

    /**
     * Helper method to get model attributes for change tracking
     */
    public static function getModelAttributes($model, array $fields = []): array
    {
        // Handle case where model is null or not an object
        if (!$model || (!is_object($model) && !is_array($model))) {
            return [];
        }

        // Handle array input (from getOriginal())
        if (is_array($model)) {
            if (empty($fields)) {
                return $model;
            }

            $attributes = [];
            foreach ($fields as $field) {
                $attributes[$field] = $model[$field] ?? null;
            }
            return $attributes;
        }

        // Handle model object
        if (empty($fields)) {
            return method_exists($model, 'getAttributes') ? $model->getAttributes() : [];
        }

        $attributes = [];
        foreach ($fields as $field) {
            if (method_exists($model, 'getAttribute')) {
                $attributes[$field] = $model->getAttribute($field);
            } elseif (is_object($model) && property_exists($model, $field)) {
                $attributes[$field] = $model->$field;
            } else {
                $attributes[$field] = null;
            }
        }

        return $attributes;
    }

    /**
     * Helper method to format field names for display
     */
    private static function formatFieldName(string $fieldName): string
    {
        // Convert snake_case to Title Case
        return ucwords(str_replace('_', ' ', $fieldName));
    }

    /**
     * Helper method to easily track model changes
     * Usage: LogService::trackModelChanges($model, $fields, function() use ($model) { $model->save(); });
     */
    public static function trackModelChanges($model, array $fields, callable $saveCallback): array
    {
        $originalValues = self::getModelAttributes($model->getOriginal(), $fields);

        // Execute the save operation
        $result = $saveCallback();

        $newValues = self::getModelAttributes($model, $fields);

        return [
            'original' => $originalValues,
            'new' => $newValues,
            'changes' => self::formatChanges($originalValues, $newValues),
            'result' => $result
        ];
    }
}
