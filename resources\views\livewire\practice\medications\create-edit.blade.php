<div>
    <form wire:submit.prevent="store">
        <x-layout.row>
            <div class="col">
                <x-card.body>
                    <div class="row">
                        <div class="col-md-12 mb-3" wire:ignore>
                            <label for="medication">Medication <span class="text-danger">*</span></label>
                           <select class="form-control" wire:model="medication_id" id="medication">
                               <option value="">Select Medication</option>
                               @foreach ($medications as $medication)
                                   <option value="{{ $medication->id }}">{{ $medication->name }}</option>
                               @endforeach
                           </select>
                        </div>

                    </div>
                </x-card.body>

                <x-group.errors />

                <x-card.footer>
                    <x-btn-with-loading target="store" text="Save" type="submit" />
                </x-card.footer>
            </div>
        </x-layout.row>
    </form>
</div>

@push('scripts')
<script>
    $(document).ready(function () {
        $('#medication').select2({
            placeholder: "Select Medication",
        }).on('change', function (e) {
            @this.set('medication_id', $(e.target).val());
        });
    });
</script>

@endpush