<?php

namespace App\Jobs;

use App\Models\Import;
use App\Models\ImportFile;
use App\Models\User;
use App\Services\LogService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class ScriptProcessJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public ImportFile $script;
    public User $user;

    /**
     * Create a new job instance.
     */
    public function __construct(ImportFile $script, User $user)
    {
        $this->script = $script;
        $this->user = $user;
    }


    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $script = $this->script;
        $script->update(['status' => ImportFile::STATUS_PENDING_APPROVAL]);

        // Log script signing
        LogService::logScriptSigned([
            'patient_name' => $script->first_name . ' ' . $script->last_name,
            'medication' => $script->medication,
            'script_id' => $script->id,
            'status' => $script->status
        ],$this->user);

        SendDispenseJob::dispatch([$script->id], $this->user,  [
            'total' => 1,
            'title' => 'Sending Scripts to Dispense Pro',
        ])->delay(now()->addSeconds(10));
    }
}
