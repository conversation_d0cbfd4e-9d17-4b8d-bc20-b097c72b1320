<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('import_files', function (Blueprint $table) {
            $table->string('days_supply')->after('vial_quantity')->nullable();
            $table->string('ship_to')->after('notes')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('import_files', function (Blueprint $table) {
            if (Schema::hasColumn('import_files', 'days_supply', 'ship_to')) {

                $table->dropColumn('days_supply');
                $table->dropColumn('ship_to');
            }
        });
    }
};
