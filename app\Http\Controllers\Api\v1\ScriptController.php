<?php

namespace App\Http\Controllers\Api\v1;

use App\Http\Controllers\Controller;
use App\Http\Requests\ImportCreateRequest;
use App\Http\Requests\v1\ReturnScriptRequest;
use App\Jobs\ScriptProcessJob;
use App\Models\Import;
use App\Models\ImportFile;
use App\Models\Patient;
use App\Models\User;
use App\Models\State;
use App\Models\Medication;
use App\Models\PracticeProvider;
use App\Models\PracticeMedication;
use App\Services\LogService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Str;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class ScriptController extends Controller
{
    public function index(Request $request)
    {
        $data = ImportFile::query();

        if ($request->has('search')) {
            $search = '%' . $request->search . '%';
            $data = $data->where(function ($query) use ($search) {
                $query->where('name', 'like', $search);
            });
        }

        if ($request->has('page')) {

            return response()->json(
                collect([
                    'status' => '1',
                    'message' => __('apiMessages.scripts_list_returned'),
                ])->merge($data->simplePaginate($request->has('per_page') ? $request->per_page : 10))
            );
        }

        return response()->json([
            'data' => $data->get(),
            'message' => __('apiMessages.scripts_list_returned'),
            'status' => '1',
        ]);
    }

    public function createImport(ImportCreateRequest $request)
    {
        $practice = Auth::user();

        // Find user by NPI number
        $user = User::where('NPI#', $request->provider_npi)
            ->where('role', 'provider')
            ->first();

        if (!$user) {
            return response()->json([
                'status' => '0',
                'message' => __('apiMessages.user_not_found_with_npi'),
            ]);
        }

        // Check if practice has connection with provider
        $practiceProvider = PracticeProvider::where('practice_id', $practice->id)
            ->where('provider_id', $user->id)
            ->first();

        if (!$practiceProvider) {
            return response()->json([
                'status' => '0',
                'message' => __('apiMessages.specified_provider_not_found'),

            ]);
        }

        // Check if practice has connection with medication
        $medication = Medication::where('name', $request->medication_name)->first();

        if (!$medication) {
            return response()->json([
                'status' => '0',
                'message' => __('apiMessages.medication_not_found'),

            ]);
        }

        $practiceMedication = PracticeMedication::where('practice_id', $practice->id)
            ->where('medication_id', $medication->id)
            ->first();

        if (!$practiceMedication) {
            return response()->json([
                'status' => '0',
                'message' => __('apiMessages.specified_medication_not_found'),

            ]);
        }

        // Create import
        $import = Import::create([
            'file_name' => 'API Entry',
            'user_id'   => $user->id,
        ]);

        // Handle PDF file upload
        $filePath = null;
        if ($request->hasFile('rx_pdf')) {
            $file = $request->file('rx_pdf');
            $fileName = 'prescription_' . time() . '_' . uniqid() . '.pdf';
            $filePath = $file->storeAs('prescriptions', $fileName, 'public');
        }

        $orderId = strtoupper(Str::random(3)) . rand(10000, 99999);


        // Create import_file entry using the import's ID
        $importFile = ImportFile::create([
            'import_id'  => $import->id,
            'order_id'  => $orderId,
            'file_path' => $filePath,
            'script_date' => $request->rx_date,
            'last_name' => $request->patient_last_name,
            'first_name' => $request->patient_first_name,
            'dob' => $request->patient_dob,
            'gender' => $request->patient_gender ?? null,
            'address' => $request->patient_address,
            'city' => $request->patient_city,
            'state' => strtoupper($request->patient_state),
            'zip'  => $request->patient_zip_code,
            'phone'  => $request->patient_phone ?? null,
            'medication' => $request->medication_name,
            // 'stregnth'  => $request->stregnth,
            // 'dosing'  => $request->dosing,
            'refills'  => $request->medication_refills ?? 0,
            'vial_quantity'  => $request->medication_vial_quantity ?? 1,
            'days_supply'  => $request->medication_days_supply ?? 28,
            'sig'  => $request->rx_sig ?? null,
            'notes'  => $request->rx_notes ?? null,
            'ship_to'  => $request->rx_ship_to,
            'is_eligible_for_signing'  => 1,
            'status'  => ImportFile::STATUS_NEW,
            'number'  => 1,
        ]);

        // Generate and update the filename after creation
        $importFile->update([
            'file_name' => 'prescription_' . $importFile->id . '.pdf',
            'signed_at' => $importFile->created_at->addHours(5)->addMinutes(31),
        ]);
        // Find matching state from states table by short_name
        $state = State::where('short_name', $importFile->state)->first();

        // Use the found state ID or null if not found
        $stateId = $state ? $state->id : null;

        $patient = Patient::updateOrCreate(
            [
                'first_name' => $importFile->first_name,
                'last_name'  => $importFile->last_name,
                'dob'        => $importFile->dob,
            ],
            [
                'provider_id'  => $import->user_id,
                'gender'       => $importFile->gender,
                'address'      => $importFile->address,
                'city'         => $importFile->city,
                'phone_number' => $importFile->phone,
                'zip_code'     => $importFile->zip,
                'state_id'     => $stateId,
            ]
        );
        $importFile->update([
            'patient_id' => $patient->id,
        ]);

        // Log script creation via API
        LogService::logScriptCreated([
            'patient_name' => $importFile->first_name . ' ' . $importFile->last_name,
            'medication' => $importFile->medication,
            'script_id' => $importFile->id,
            'import_id' => $import->id,
            'api_entry' => true
        ], $user);

        ScriptProcessJob::dispatch($importFile, $user)->delay(now()->addSeconds(10));

        return response()->json([
            'data' => [
                'order_id' => $importFile->order_id,
            ],
            'transaction_id' => $request->transaction_id,
            'message' => __('apiMessages.order_created_successfully'),
            'status' => '1',
        ]);
    }

    public function returnScript(ReturnScriptRequest $request)
    {
        // $practice = Auth::user();

        // Find user by NPI number
        // $user = User::where('NPI#', $request->npi)->first();

        // if (!$user) {
        //     return response()->json([
        //         'status' => '0',
        //         'message' => 'User not found with the provided NPI number.',
        //     ], 404);
        // }

        $importFile = ImportFile::find($request->import_file_id);

        if ($importFile->status == ImportFile::STATUS_PENDING_REVISION) {
            return response()->json([
                'status' => '0',
                'message' => __('apiMessages.script_already_returned_for_revision'),
            ]);
        }

        if ($importFile) {
            $importFile->comment = $request->reason ?? 'Recall by provider';
            // $importFile->returned_by_user_id = $user->id;
            $importFile->returned_by_user_id = $request->user_id;

            $importFile->signed_at = null;
            $importFile->status = ImportFile::STATUS_PENDING_REVISION;
            $importFile->is_eligible_for_signing = false;
            $importFile->save();

            // Create directory for storing PDFs if needed
            $storagePath = 'public/prescriptions/' . $importFile->import_id;
            Storage::makeDirectory($storagePath);

            // Get the logged-in user and state information
            $user = User::find($importFile->import->user_id);

            $userState = null;
            $doctorName = 'Dr. April'; // Default name

            if ($user) {
                // Get the user's full name for the signature
                $doctorName = $user->printed_name ?? ($user->first_name . ' ' . $user->last_name);

                // Get the user's state information if available
                if ($user->state_id) {
                    $userState = State::find($user->state_id);
                }
            }

            // Format dates properly
            $scriptDateFormatted = $importFile->script_date;
            if ($scriptDateFormatted && !is_string($scriptDateFormatted)) {
                try {
                    $scriptDateFormatted = Carbon::parse($scriptDateFormatted)->format('m/d/Y');
                } catch (\Exception) {
                    // Keep original if parsing fails
                }
            }

            $dobFormatted = $importFile->dob;
            if ($dobFormatted && !is_string($dobFormatted)) {
                try {
                    $dobFormatted = Carbon::parse($dobFormatted)->format('m/d/Y');
                } catch (\Exception) {
                    // Keep original if parsing fails
                }
            }

            // Prepare data array for PDF generation
            $data = [
                $scriptDateFormatted, // 0: Script date
                $importFile->last_name,   // 1: Last name
                $importFile->first_name,  // 2: First name
                $dobFormatted,      // 3: DOB
                $importFile->gender,      // 4: Gender
                $importFile->address,     // 5: Address
                $importFile->city,        // 6: City
                $importFile->state,       // 7: State
                $importFile->zip,         // 8: Zip
                $importFile->phone,       // 9: Phone
                $importFile->medication,  // 10: Medication
                $importFile->stregnth,    // 11: Strength
                $importFile->dosing,      // 12: Dosing
                $importFile->refills,     // 13: Refills
                $importFile->vial_quantity, // 14: Vial quantity
                $importFile->days_supply, // 15: Days supply
                $importFile->sig,         // 16: Sig
                $importFile->notes,       // 17: Notes
                $importFile->ship_to,     // 18: Ship to
            ];

            // Generate PDF
            $pdf = PDF::loadView('pdf.new-prescription', [
                'data' => $data,
                'isPdfDownload' => false,
                'user' => $user,
                'userState' => $userState,
                'doctorName' => $doctorName,
                'isSigned' => false,
            ]);
            $pdf->setPaper('letter');


            // Save the PDF to storage
            Storage::put($importFile->file_path, $pdf->output());

            return response()->json([
                'status' => '1',
                'message' => __('apiMessages.script_returned_for_revision'),
            ]);
        }
    }
}
