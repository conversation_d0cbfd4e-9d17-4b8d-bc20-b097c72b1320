<?php

namespace App\Http\Livewire\Settings;

use App\Models\SuperAdmin;
use App\Rules\NotSameAsOldPassword;
use App\Services\LogService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Livewire\Component;

class ChangePassword extends Component
{
    public $old_password, $password, $password_confirmation, $user;

    public function rules()
    {
        return [
            'old_password' => 'required|current_password:web',
            'password' => ['required', new NotSameAsOldPassword($this->user)],
            'password_confirmation' => 'required|same:password',
        ];
    }

    protected $messages = [
        'old_password.password' => 'The entered password does not match your current password.',
    ];

    public function render()
    {
        return view('livewire.settings.change-password');
    }

    public function mount()
    {
        $this->user = Auth::user();
    }

    public function store()
    {
        $this->validate();

        $user = $this->user;
        $user->password = bcrypt($this->password);
        $user->password_changed_at = now(); // Set the password_changed_at timestamp
        $user->save();

        // Log password change
        LogService::logPasswordChanged($user);

        $this->reset([
            'old_password',
            'password',
            'password_confirmation',
        ]);

        // Show success message
        $this->emit('alert', ['type' => 'success', 'message' => __('messages.password_changed_successfully')]);

        // Add a small delay to ensure the alert is shown before logout
        $this->emit('passwordChanged');
    }

    /**
     * Log out the user after password change
     */
    public function logout()
    {
        // Log the user out
        Auth::logout();

        // Invalidate the session
        Session::invalidate();

        // Regenerate the CSRF token
        Session::regenerateToken();

        // Redirect to login page
        return redirect()->route('login')->with('status', __('messages.password_changed_successfully'));
    }
}
