<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\GetDatatableRequest;
use App\Http\Resources\DataTableCollection;
use App\Models\ScriptTemplate;
use Illuminate\Http\Request;

class ScriptTemplateController extends Controller
{
    public function index()
    {
        //
    }

    public function delete(ScriptTemplate $script_template)
    {
        $script_template->delete();

        return response([
            'message' => "Script Template Deleted sucessfully!",
            'status' => '1',
        ]);
    }

    public function indexWebAll(GetDatatableRequest $request)
    {
        $data = ScriptTemplate::query();
        $search = $request->input('query.search');
        $sort_order = $request->input('sort.sort', '');
        $sort_field = $request->input('sort.field', '');

        if ($search) {
            $search = '%' . $search . '%';
            $data->where('title', 'like', $search);
        }

        return new DataTableCollection(
            $data->paginate(
                $request->pagination['perpage'],
                ['*'],
                'page',
                $request->pagination['page']
            )
        );
    }

    public function listTemplate()
    {
        $page_title = 'Script Template List';

        return view('scripts.admin.template.index', [
            'page_title' => $page_title,
        ]);
    }

    public function addTemplate()
    {
        $page_title = 'Script Template Create';
        $livewire_component = 'template.create-edit';

        $livewire_data = [
            'page_title' => $page_title,
            'script_template' => new ScriptTemplate()
        ];

        return view('layouts.livewire', [
            'page_title' => $page_title,
            'livewire_component' => $livewire_component,
            'livewire_data' => $livewire_data,
        ]);
    }

    public function editTemplate(ScriptTemplate $script_template)
    {
        $page_title = 'Script Template Create';
        $livewire_component = 'template.create-edit';

        $livewire_data = [
            'page_title' => $page_title,
            'script_template' => $script_template
        ];

        return view('layouts.livewire', [
            'page_title' => $page_title,
            'livewire_component' => $livewire_component,
            'livewire_data' => $livewire_data,
        ]);
    }
}
