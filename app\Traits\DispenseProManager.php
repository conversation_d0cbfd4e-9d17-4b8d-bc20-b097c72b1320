<?php

namespace App\Traits;

use App\Exceptions\CustomException;
use App\Services\ProgressTrackingService;
use Illuminate\Support\Facades\Log;
use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;

trait DispenseProManager
{
    /**
     * Sets up HTTP client with authentication token and base URL for DispensePro operations
     *
     * @throws Exception If environment variables are not properly configured
     * @return \Illuminate\Http\Client\PendingRequest
     */
    static function setUpDispenseProHttpClient()
    {
        $username = config('dispensepro.username');
        $password = config('dispensepro.password');
        $base_url = config('dispensepro.base_url');

        if (!$username || !$password || !$base_url) {
            throw new Exception("Please set up .env file");
        }
        $http = Http::withBasicAuth($username, $password);

        return $http;
    }


    static function DispenseProCreateOrder(array $data)
    {

        $http = DispenseProManager::setUpDispenseProHttpClient();

        try {

            $response = $http
                ->asJson()
                ->post(config('dispensepro.base_url') . '?n=API', $data);

            if ($response->getStatusCode() != 200) {
                if ($response->getStatusCode() != 400) {
                    // throw new CustomException($response->getBody()->getContents(), $response->getStatusCode());
                    Log::info("DispensePro order failed, reason: " . json_encode($response['errorMessages']));
                }
            }

            // Return the JSON response as an array
            return $response->json();
        } catch (CustomException $e) {
            throw $e;
        } catch (\Throwable $th) {
            throw new Exception($th->getMessage());
        }
    }

    static function GetProviders()
    {
        $http = DispenseProManager::setUpDispenseProHttpClient();

        try {
            $response = $http->asForm()->post('/dis/servlet/dis.Main', [
                'n'        => 'RemoteLogin',
                'siteId'   => config('dispensepro.site_id'),
                'username' => config('dispensepro.username'),
                'password' => config('dispensepro.password'),
                'target'   => 'ShowProviders',
            ]);

            if ($response->status() !== 200) {
                throw new CustomException($response->body(), $response->status());
            }

            return $response;
        } catch (CustomException $e) {
            throw $e;
        } catch (\Throwable $th) {
            throw new \Exception("DispensePro Provider Fetch Error: " . $th->getMessage());
        }
    }

    public static function DispenseProVoidOrder(array|string $orderIds): array
    {
        $http = self::setUpDispenseProHttpClient();

        set_time_limit(1500000);

        // Normalize to array
        $ids = is_array($orderIds) ? $orderIds : [$orderIds];

        $results = [];
        $total = count($ids);
        $processed = 0;

        $progressTrackingService = new ProgressTrackingService();

        foreach ($ids as $id) {
            $payload = [
                'action'  => 'void',
                'orderId' => $id,
            ];

            try {
                $response = $http
                    ->asJson() // ✅ Important: send as application/json
                    ->post(config('dispensepro.base_url') . '?n=API', $payload);

                $status = $response->status();

                if ($status !== 200) {
                    throw new CustomException("Error voiding order {$id}: " . $response->body(), $status);
                }

                $results[] = [
                    'orderId'  => $id,
                    'status'   => $status,
                    'response' => $response->json(),
                ];
            } catch (CustomException $e) {
                $results[] = [
                    'orderId'  => $id,
                    'status'   => $e->getCode() ?: 500,
                    'response' => [
                        'error' => $e->getMessage(),
                    ],
                ];
            } catch (\Throwable $th) {
                $results[] = [
                    'orderId'  => $id,
                    'status'   => 500,
                    'response' => [
                        'error' => "DispensePro Void Order Error: " . $th->getMessage(),
                    ],
                ];
            }

            $processed++;

            $progressTrackingService->updateProgress("queue_progress", [
                'processed' => $processed,
                'total' => $total,
            ]);
        }

        return $results;
    }
}
