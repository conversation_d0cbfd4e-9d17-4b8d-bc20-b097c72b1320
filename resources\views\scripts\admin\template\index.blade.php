@extends('master')

@php
use App\Models\ImportFile;
@endphp

@section('content')
<div class="card card-custom mb-5">

    <div class="card-body" x-data="{ showFilter: false }">

        <div class="row justify-content-between">
            <div class="col-12 col-sm-8 col-md-6 col-lg-4 col-xl-3 mb-6">
                <div class="input-icon">
                    <input type="text" class="form-control" placeholder="Search..." id="users_search" />
                    <span>
                        <i class="flaticon2-search-1 text-muted"></i>
                    </span>
                </div>
            </div>
            <div class="col-auto">
                <a href="{{ route('scripts.template.create') }}" class="btn btn-md btn-dark">Add Template</a>
            </div>
        </div>
        <div class="datatable datatable-bordered datatable-head-custom" id="script_template_dt"></div>

    </div>
</div>

<!-- Script Preview Modal -->
<div class="modal fade" id="scriptPreviewModal" tabindex="-1" role="dialog" aria-labelledby="scriptPreviewModalLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="scriptPreviewModalLabel">Script Preview</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <i class="ki ki-close"></i>
                </button>
            </div>
            <div class="modal-body">
                <div id="script-preview-content">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="sr-only">Loading...</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <a href="#" id="download-preview-btn" class="btn btn-primary">
                    <i class="fas fa-download"></i> Download
                </a>
                <button type="button" id="send-preview-btn" class="btn btn-success">
                    <i class="fas fa-paper-plane"></i> Send
                </button>
            </div>
        </div>
    </div>
</div>
@endsection
@section('styles')
<style>
    button:disabled {
        cursor: not-allowed !important;
    }

    .text-primary {
        text-decoration: none;
    }

    .text-primary:hover {
        text-decoration: underline;
    }

    /* Custom styles for the preview modal */
    #scriptPreviewModal .modal-dialog {
        max-width: 95%;
        height: 95vh;
        margin: 0.5rem auto;
    }

    #scriptPreviewModal .modal-content {
        height: 100%;
        border-radius: 4px;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
        display: flex;
        flex-direction: column;
    }

    #scriptPreviewModal .modal-body {
        flex: 1;
        overflow: hidden;
        padding: 0;
    }

    #scriptPreviewModal .modal-header {
        border-bottom: 1px solid #ebedf3;
        padding: 1rem 1.75rem;
    }

    #scriptPreviewModal .modal-footer {
        border-top: 1px solid #ebedf3;
        padding: 1rem 1.75rem;
        position: relative;
        flex-shrink: 0;
        justify-content: flex-end;
        background-color: #fff;
        z-index: 5;
    }

    #scriptPreviewModal .close {
        cursor: pointer;
        font-size: 1.25rem;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 2rem;
        height: 2rem;
        margin: 0;
        padding: 0;
    }

    #scriptPreviewModal .close i {
        font-size: 1rem;
    }

    #scriptPreviewModal .close:hover {
        color: #3699FF;
        background-color: #f3f6f9;
        border-radius: 4px;
    }

    #script-preview-content {
        height: 100%;
        width: 100%;
        overflow: hidden;
        position: relative;
    }

    #script-preview-content iframe {
        width: 100%;
        height: 100%;
        border: none;
        display: block;
    }
</style>
@endsection
@section('scripts')
<script src="https://cdn.jsdelivr.net/gh/alpinejs/alpine@v2.7.0/dist/alpine.min.js" defer></script>
<script>
    var datatable;
    var datatableElement;
    var searchElement;
    var columnArray;

    const storagePath = `{{ url('/storage') }}`;
    const apiRoute = `{{ route('scripts.template.api.all') }}`;
    let url = "{{ Storage::url('/') }}";
    const deleteRoute = `{{ route('scripts.template.delete', ['script_template' => '::ID']) }}`;
    const viewRoute = `{{ route('scripts.preview', ['importId' => '::ID']) }}`;
    const editRoute = `{{ route('scripts.template.edit', ['script_template' => '::ID']) }}`;

    datatableElement = $('#script_template_dt');
    searchElement = $('#users_search');

    columnArray = [{
            field: 'created_at',
            title: `Created At`,
            autoHide: false,
            sortable: true,
            width: 'auto',
            template: function(data) {
                return data.created_at ? moment(data.created_at).format('MM/DD/YYYY  hh:mm A') : '';
            }
        },
        {
            field: 'title',
            title: `Title`,
            sortable: true,
            autoHide: false
        },
        {
            field: 'Actions',
            title: 'Actions',
            width: 'auto',
            sortable: false,
            overflow: 'visible',
            autoHide: false,
            template: function(data) {
                const editBtn = `<a href="${editRoute.replace('::ID', data.id)}" data-id="${data.id}" class="btn btn-sm btn-clean btn-icon" data-toggle="tooltip" title="Edit Script">
                 <i class="menu-icon fas fa-pen"></i>
            </a>`;
                const deleteBtn = ` <a href="#" data-id="${data.id}" class="btn btn-sm btn-clean btn-icon delete-script-template-btn" data-toggle="tooltip" title="Delete Script">
                <i class="menu-icon fas fa-trash"></i>
            </a>`;

                return editBtn + deleteBtn;
            }
        }
    ];

    datatable = datatableElement.KTDatatable({
        data: {
            type: 'remote',
            source: {
                read: {
                    url: apiRoute,
                    //sample custom headers
                    headers: {
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    map: function(raw) {
                        // sample data mapping
                        var dataSet = raw;
                        if (typeof raw.data !== 'undefined') {
                            dataSet = raw.data;
                        }
                        return dataSet;
                    },
                },
            },
            pageSize: 10,
            serverPaging: true,
            serverFiltering: true,
            serverSorting: true,
        },
        pagination: true,
        search: {
            input: searchElement,
            key: 'search'
        },
        layout: {
            customScrollbar: false,
            scroll: true,
        },
        columns: columnArray
    });

    // Handle delete script button click
    $(document).on('click', '.delete-script-template-btn', function(e) {
        e.preventDefault();

        const scriptTemplateId = $(this).data('id');
        Swal.fire({
            title: 'Are you sure?',
            text: "Are you sure you want to delete this script template?",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes',
            cancelButtonText: 'No'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: deleteRoute.replace('::ID', scriptTemplateId),
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    success: function(res) {
                        if (res.status === '1') {
                            toastr.success(res.message);
                            datatable.reload();
                        } else {
                            toastr.error(res.message);
                        }
                    },
                    error: function(xhr) {
                        let message = 'Error deleting script template';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            message = xhr.responseJSON.message;
                        }
                        toastr.error(message);
                    }
                });
            }
        });
    });
</script>
@endsection