<div>
    <?php if(session()->has('message')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo e(session('message')); ?>

            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    <?php endif; ?>

    <form wire:submit.prevent="savePrescription">
        <?php if (isset($component)) { $__componentOriginal3c8f9b7880ed8037e1c74e30535cdc4c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal3c8f9b7880ed8037e1c74e30535cdc4c = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.row','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('layout.row'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
            <div class="col">
                
                <?php if (isset($component)) { $__componentOriginalcc4d6dcf44f2ce2176eee5939e2828e6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalcc4d6dcf44f2ce2176eee5939e2828e6 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.card.body','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('card.body'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
                    
                    <div class="row">
                        <!-- Left Column -->
                        <div class="col-md-6 mt-4">
                            

                            <fieldset class="form-group-box">
                                <legend class="w-auto px-2 legend">Demographics</legend>
                                <!-- Script Date -->
                                <div class="mb-3">
                                    <?php if (isset($component)) { $__componentOriginal12acffbf805bbedccff0d363d862d8e0 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal12acffbf805bbedccff0d363d862d8e0 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.input.text','data' => ['label' => 'Script Date','labelRequired' => '1','model' => 'script_date_display','id' => 'script_date','readonly' => 'readonly']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.input.text'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'Script Date','labelRequired' => '1','model' => 'script_date_display','id' => 'script_date','readonly' => 'readonly']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $attributes = $__attributesOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $component = $__componentOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__componentOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
                                    
                                </div>
                                <!-- Last Name -->
                                <div class="mb-3">
                                    <div class="form-group">
                                        <label for="last_name" class="form-label">Last Name <span
                                                class="text-danger">*</span></label>
                                        <div class="input-group" id="last_name_group">
                                            <input type="text" id="last_name"
                                                class="form-control <?php $__errorArgs = ['importFile.last_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                wire:model.debounce.500ms="importFile.last_name"
                                                wire:input="checkAndResetPatientSelection($event.target.value, 'last_name')"
                                                wire:focus="$set('activeField', 'last_name')"
                                                wire:blur="$set('activeField', null)"
                                                <?php if($selectedPatientId): ?> wire:keydown="searchPatients($event.target.value, 'last_name')"
                                                        wire:keydown.arrow-down="highlightNext"
                                                        wire:keydown.arrow-up="highlightPrevious"
                                                        wire:keydown.enter.prevent="selectHighlightedPatient" <?php endif; ?>
                                                placeholder="Enter last name">
                                            <?php if(count($matchingPatients) > 0 &&
                                                    $activeField === 'last_name' &&
                                                    !empty(trim($importFile->last_name))): ?>
                                                <div class="dropdown-menu show w-100 mt-0" id="last_name_dropdown">
                                                    <?php $__currentLoopData = $matchingPatients; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $i => $patient): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <button type="button"
                                                            class="dropdown-item <?php echo e($highlightedIndex === $i ? 'active' : ''); ?>"
                                                            wire:click="selectPatient(<?php echo e($patient->id); ?>)">
                                                            <div>
                                                                <strong>
                                                                    <?php echo e($patient->first_name); ?> <?php echo e($patient->last_name); ?>

                                                                    <span
                                                                        class="text-muted"><?php echo e($patient->dob ? date('m/d/Y', strtotime($patient->dob)) : ''); ?>

                                                                    </span>
                                                                </strong>
                                                            </div>
                                                        </button>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        <?php if (isset($component)) { $__componentOriginal8a61cf4ce6144d9e2012fbc98db0155f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8a61cf4ce6144d9e2012fbc98db0155f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.error','data' => ['model' => 'importFile.last_name']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.error'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['model' => 'importFile.last_name']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8a61cf4ce6144d9e2012fbc98db0155f)): ?>
<?php $attributes = $__attributesOriginal8a61cf4ce6144d9e2012fbc98db0155f; ?>
<?php unset($__attributesOriginal8a61cf4ce6144d9e2012fbc98db0155f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8a61cf4ce6144d9e2012fbc98db0155f)): ?>
<?php $component = $__componentOriginal8a61cf4ce6144d9e2012fbc98db0155f; ?>
<?php unset($__componentOriginal8a61cf4ce6144d9e2012fbc98db0155f); ?>
<?php endif; ?>
                                    </div>
                                </div>

                                <!-- First Name -->
                                <div class="mb-3">
                                    <div class="form-group">
                                        <label for="first_name" class="form-label">First Name <span
                                                class="text-danger">*</span></label>
                                        <div class="input-group" id="first_name_group">
                                            <input type="text" id="first_name"
                                                class="form-control <?php $__errorArgs = ['importFile.first_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                wire:model.debounce.500ms="importFile.first_name"
                                                wire:input="checkAndResetPatientSelection($event.target.value, 'first_name')"
                                                wire:focus="$set('activeField', 'first_name')"
                                                wire:blur="$set('activeField', null)"
                                                <?php if($selectedPatientId): ?> wire:keydown="searchPatients($event.target.value, 'first_name')"
                                                        wire:keydown.arrow-down="highlightNext"
                                                        wire:keydown.arrow-up="highlightPrevious"
                                                        wire:keydown.enter.prevent="selectHighlightedPatient" <?php endif; ?>
                                                placeholder="Enter first name">
                                            <?php if(count($matchingPatients) > 0 &&
                                                $activeField === 'first_name' &&
                                                    !empty(trim($importFile->first_name))): ?>
                                                <div class="dropdown-menu show w-100 mt-0" id="first_name_dropdown">
                                                    <?php $__currentLoopData = $matchingPatients; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $i => $patient): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <button type="button"
                                                            class="dropdown-item <?php echo e($highlightedIndex === $i ? 'active' : ''); ?>"
                                                            wire:click="selectPatient(<?php echo e($patient->id); ?>)">
                                                            <div>
                                                                <strong>
                                                                    <?php echo e($patient->first_name); ?> <?php echo e($patient->last_name); ?>

                                                                    <span
                                                                        class="text-muted"><?php echo e($patient->dob ? date('m/d/Y', strtotime($patient->dob)) : ''); ?>

                                                                    </span>
                                                                </strong>
                                                            </div>
                                                        </button>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        <?php if (isset($component)) { $__componentOriginal8a61cf4ce6144d9e2012fbc98db0155f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8a61cf4ce6144d9e2012fbc98db0155f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.error','data' => ['model' => 'importFile.first_name']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.error'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['model' => 'importFile.first_name']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8a61cf4ce6144d9e2012fbc98db0155f)): ?>
<?php $attributes = $__attributesOriginal8a61cf4ce6144d9e2012fbc98db0155f; ?>
<?php unset($__attributesOriginal8a61cf4ce6144d9e2012fbc98db0155f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8a61cf4ce6144d9e2012fbc98db0155f)): ?>
<?php $component = $__componentOriginal8a61cf4ce6144d9e2012fbc98db0155f; ?>
<?php unset($__componentOriginal8a61cf4ce6144d9e2012fbc98db0155f); ?>
<?php endif; ?>
                                    </div>
                                </div>

                                <!-- Date of Birth -->
                                <div class="mb-3">
                                    <?php if (isset($component)) { $__componentOriginal12acffbf805bbedccff0d363d862d8e0 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal12acffbf805bbedccff0d363d862d8e0 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.input.text','data' => ['label' => 'Date of Birth','labelRequired' => '1','model' => 'dob_display','id' => 'dob_individual_date_input','readonly' => 'readonly']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.input.text'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'Date of Birth','labelRequired' => '1','model' => 'dob_display','id' => 'dob_individual_date_input','readonly' => 'readonly']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $attributes = $__attributesOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $component = $__componentOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__componentOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
                                </div>

                                <!-- Gender -->
                                <div class="mb-3">
                                    <div class="form-group">
                                        <div wire:ignore>
                                            <label for="gender" class="form-label">Gender</label><span
                                                class="text-danger">*</span>
                                            <select wire:model="importFile.gender" id="gender"
                                                class="form-control gender-select">
                                                <option value="">Select Gender</option>
                                                <option value="M">Male</option>
                                                <option value="F">Female</option>
                                            </select>
                                        </div>
                                        <?php if (isset($component)) { $__componentOriginal8a61cf4ce6144d9e2012fbc98db0155f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8a61cf4ce6144d9e2012fbc98db0155f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.error','data' => ['model' => 'importFile.gender']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.error'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['model' => 'importFile.gender']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8a61cf4ce6144d9e2012fbc98db0155f)): ?>
<?php $attributes = $__attributesOriginal8a61cf4ce6144d9e2012fbc98db0155f; ?>
<?php unset($__attributesOriginal8a61cf4ce6144d9e2012fbc98db0155f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8a61cf4ce6144d9e2012fbc98db0155f)): ?>
<?php $component = $__componentOriginal8a61cf4ce6144d9e2012fbc98db0155f; ?>
<?php unset($__componentOriginal8a61cf4ce6144d9e2012fbc98db0155f); ?>
<?php endif; ?>
                                    </div>
                                </div>
                            </fieldset>
                        </div>

                        <!-- Right Column -->
                        <div class="col-md-6 mt-4">
                            
                            <fieldset class="form-group-box">
                                <legend class="w-auto px-2 legend">Contact</legend>
                                <div class="mb-3">
                                    <?php if (isset($component)) { $__componentOriginal12acffbf805bbedccff0d363d862d8e0 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal12acffbf805bbedccff0d363d862d8e0 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.input.text','data' => ['label' => 'Address','labelRequired' => '1','model' => 'importFile.address']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.input.text'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'Address','labelRequired' => '1','model' => 'importFile.address']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $attributes = $__attributesOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $component = $__componentOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__componentOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
                                </div>

                                <div class="mb-3">
                                    <?php if (isset($component)) { $__componentOriginal12acffbf805bbedccff0d363d862d8e0 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal12acffbf805bbedccff0d363d862d8e0 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.input.text','data' => ['label' => 'City','labelRequired' => '1','model' => 'importFile.city']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.input.text'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'City','labelRequired' => '1','model' => 'importFile.city']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $attributes = $__attributesOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $component = $__componentOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__componentOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
                                </div>

                                <div class="mb-3">
                                    <div class="form-group">
                                        <div wire:ignore>
                                            <label for="state" class="form-label">State</label><span
                                                class="text-danger">*</span>
                                            <select wire:model="importFile.state" id="state"
                                                class="form-control gender-select <?php echo e($errors->has('importFile.state') ? 'is-invalid' : ''); ?>">
                                                <option value="">Select State</option>
                                                <?php $__currentLoopData = $states; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $state): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e($state->short_name); ?>"
                                                        data-id="<?php echo e($state->id); ?>">
                                                        <?php echo e($state->name); ?>

                                                    </option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                        </div>
                                        <?php $__errorArgs = ['importFile.state'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback d-block"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <?php if (isset($component)) { $__componentOriginal12acffbf805bbedccff0d363d862d8e0 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal12acffbf805bbedccff0d363d862d8e0 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.input.text','data' => ['label' => 'Zip Code','labelRequired' => '1','model' => 'importFile.zip']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.input.text'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'Zip Code','labelRequired' => '1','model' => 'importFile.zip']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $attributes = $__attributesOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $component = $__componentOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__componentOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
                                </div>

                                <div class="mb-3">
                                    <?php if (isset($component)) { $__componentOriginal12acffbf805bbedccff0d363d862d8e0 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal12acffbf805bbedccff0d363d862d8e0 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.input.text','data' => ['label' => 'Phone','labelRequired' => '0','model' => 'importFile.phone','type' => 'number']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.input.text'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'Phone','labelRequired' => '0','model' => 'importFile.phone','type' => 'number']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $attributes = $__attributesOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $component = $__componentOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__componentOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
                                </div>
                            </fieldset>
                        </div>
                    </div>

                    <!-- Medication Info -->
                    <fieldset class="form-group-box">
                        <legend class="w-auto px-2 legend">Medication</legend>
                        <div class="row mt-4">
                            <div class="col-12">
                                
                                <div class="mb-3">
                                    <div class="form-group" wire:ignore>
                                        <label labelRequired="1" for="medication" class="form-label">
                                            Medication
                                            <span class="text-danger">*</span>
                                        </label>
                                        <select label="medication" id="medication"
                                            class="form-control <?php $__errorArgs = ['importFile.medication'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?> gender-select"
                                            wire:model="importFile.medication" placeholder="Select medication">
                                            <option value="" selected>Select medication</option>
                                            <?php $__currentLoopData = $medications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $medication): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($medication->name); ?>"><?php echo e($medication->name); ?>

                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>

                                        <?php $__errorArgs = ['importFile.medication'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <span
                                                class="form-text text-danger"><strong><?php echo e($message); ?></strong></span>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                                

                                    <div class="mb-3">
                                        <?php if (isset($component)) { $__componentOriginal12acffbf805bbedccff0d363d862d8e0 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal12acffbf805bbedccff0d363d862d8e0 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.input.text','data' => ['label' => 'Refills','labelRequired' => '1','model' => 'importFile.refills']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.input.text'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'Refills','labelRequired' => '1','model' => 'importFile.refills']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $attributes = $__attributesOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $component = $__componentOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__componentOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
                                    </div>

                                    <div class="mb-3">
                                        <?php if (isset($component)) { $__componentOriginal12acffbf805bbedccff0d363d862d8e0 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal12acffbf805bbedccff0d363d862d8e0 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.input.text','data' => ['label' => 'Vial Quantity','labelRequired' => '1','model' => 'importFile.vial_quantity']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.input.text'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'Vial Quantity','labelRequired' => '1','model' => 'importFile.vial_quantity']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $attributes = $__attributesOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $component = $__componentOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__componentOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
                                    </div>
                                    <div class="mb-3">
                                        <?php if (isset($component)) { $__componentOriginal12acffbf805bbedccff0d363d862d8e0 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal12acffbf805bbedccff0d363d862d8e0 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.input.text','data' => ['label' => 'Days Supply','labelRequired' => '1','model' => 'importFile.days_supply','type' => 'number','min' => '0']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.input.text'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'Days Supply','labelRequired' => '1','model' => 'importFile.days_supply','type' => 'number','min' => '0']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $attributes = $__attributesOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__attributesOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal12acffbf805bbedccff0d363d862d8e0)): ?>
<?php $component = $__componentOriginal12acffbf805bbedccff0d363d862d8e0; ?>
<?php unset($__componentOriginal12acffbf805bbedccff0d363d862d8e0); ?>
<?php endif; ?>
                                    </div>

                                <div class="mb-3" wire:ignore>
                                    <label for="ship_to" class="form-label">Ship To <span
                                            class="text-danger">*</span></label>
                                    <select wire:model="importFile.ship_to" id="ship_to"
                                        class="form-control <?php $__errorArgs = ['importFile.ship_to'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?> ship-select">
                                        <option value="patient" selected>Patient</option>
                                        <option value="practice">Practice</option>
                                    </select>
                                    <?php $__errorArgs = ['importFile.ship_to'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <span class="form-text text-danger"><strong><?php echo e($message); ?></strong></span>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <input type="hidden" id="comment" value="<?php echo e($importFile->comment); ?>">
                                <input type="hidden" id="operator_name"
                                    value="<?php echo e($importFile->returnedByUser ? $importFile->returnedByUser->first_name . ' ' . $importFile->returnedByUser->last_name : ''); ?>">
                                <input type="hidden" id="operator_email"
                                    value="<?php echo e($importFile->returnedByUser ? $importFile->returnedByUser->email : ''); ?>">

                                <!-- Additional Information -->
                                <div class="row mt-4">
                                    <div class="col-md-6">
                                        
                                        <div class="mb-3">
                                            <?php if (isset($component)) { $__componentOriginale04d29a08d4bf79b30ca9866e1e5794c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale04d29a08d4bf79b30ca9866e1e5794c = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.input.textarea','data' => ['label' => 'SIG','labelRequired' => '0','model' => 'importFile.sig']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.input.textarea'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'SIG','labelRequired' => '0','model' => 'importFile.sig']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale04d29a08d4bf79b30ca9866e1e5794c)): ?>
<?php $attributes = $__attributesOriginale04d29a08d4bf79b30ca9866e1e5794c; ?>
<?php unset($__attributesOriginale04d29a08d4bf79b30ca9866e1e5794c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale04d29a08d4bf79b30ca9866e1e5794c)): ?>
<?php $component = $__componentOriginale04d29a08d4bf79b30ca9866e1e5794c; ?>
<?php unset($__componentOriginale04d29a08d4bf79b30ca9866e1e5794c); ?>
<?php endif; ?>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        
                                        <div class="mb-3">
                                            <?php if (isset($component)) { $__componentOriginale04d29a08d4bf79b30ca9866e1e5794c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale04d29a08d4bf79b30ca9866e1e5794c = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.input.textarea','data' => ['label' => 'Note','labelRequired' => '0','model' => 'importFile.notes','rows' => '3']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.input.textarea'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'Note','labelRequired' => '0','model' => 'importFile.notes','rows' => '3']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale04d29a08d4bf79b30ca9866e1e5794c)): ?>
<?php $attributes = $__attributesOriginale04d29a08d4bf79b30ca9866e1e5794c; ?>
<?php unset($__attributesOriginale04d29a08d4bf79b30ca9866e1e5794c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale04d29a08d4bf79b30ca9866e1e5794c)): ?>
<?php $component = $__componentOriginale04d29a08d4bf79b30ca9866e1e5794c; ?>
<?php unset($__componentOriginale04d29a08d4bf79b30ca9866e1e5794c); ?>
<?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </fieldset>

                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalcc4d6dcf44f2ce2176eee5939e2828e6)): ?>
<?php $attributes = $__attributesOriginalcc4d6dcf44f2ce2176eee5939e2828e6; ?>
<?php unset($__attributesOriginalcc4d6dcf44f2ce2176eee5939e2828e6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalcc4d6dcf44f2ce2176eee5939e2828e6)): ?>
<?php $component = $__componentOriginalcc4d6dcf44f2ce2176eee5939e2828e6; ?>
<?php unset($__componentOriginalcc4d6dcf44f2ce2176eee5939e2828e6); ?>
<?php endif; ?>

                <?php if (isset($component)) { $__componentOriginal2f4c20c75f25d521b59e2ab32e77183d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2f4c20c75f25d521b59e2ab32e77183d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.group.errors','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('group.errors'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2f4c20c75f25d521b59e2ab32e77183d)): ?>
<?php $attributes = $__attributesOriginal2f4c20c75f25d521b59e2ab32e77183d; ?>
<?php unset($__attributesOriginal2f4c20c75f25d521b59e2ab32e77183d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2f4c20c75f25d521b59e2ab32e77183d)): ?>
<?php $component = $__componentOriginal2f4c20c75f25d521b59e2ab32e77183d; ?>
<?php unset($__componentOriginal2f4c20c75f25d521b59e2ab32e77183d); ?>
<?php endif; ?>

                <?php if (isset($component)) { $__componentOriginal7274efa5ddca416a8c7d5935f6f9a8c0 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7274efa5ddca416a8c7d5935f6f9a8c0 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.card.footer','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('card.footer'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
                    <?php if($importFile->id): ?>
                        <button type="submit" class="btn btn-primary px-5" wire:loading.attr="disabled"
                            wire:target="savePrescription">
                            <span wire:loading.remove wire:target="savePrescription">Save</span>
                            <span wire:loading wire:target="savePrescription" style="display: none;">
                                <i class="fas fa-spinner fa-spin mr-1"></i>
                                Saving...
                            </span>
                        </button>
                        <?php if(Auth::user()->role === \App\Models\User::ROLE_PROVIDER): ?>
                            <button type="button" class="btn btn-primary px-5" wire:click="saveAndSignPrescription"
                                wire:loading.attr="disabled" wire:target="saveAndSignPrescription">
                                <span wire:loading.remove wire:target="saveAndSignPrescription">Save & Sign</span>
                                <span wire:loading wire:target="saveAndSignPrescription" style="display: none;">
                                    <i class="fas fa-spinner fa-spin mr-1"></i>
                                    Saving...
                                </span>
                            </button>
                        <?php endif; ?>
                    <?php else: ?>
                        <button type="submit" class="btn btn-primary px-5" wire:loading.attr="disabled"
                            wire:target="savePrescription">
                            <span wire:loading.remove wire:target="savePrescription">Save</span>
                            <span wire:loading wire:target="savePrescription" style="display: none;">
                                <i class="fas fa-spinner fa-spin mr-1"></i>
                                Saving...
                            </span>
                        </button>
                    <?php endif; ?>

                    <a href="<?php echo e(route('scripts.ready-to-sign')); ?>" class="btn btn-outline-secondary px-5">Cancel</a>

                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7274efa5ddca416a8c7d5935f6f9a8c0)): ?>
<?php $attributes = $__attributesOriginal7274efa5ddca416a8c7d5935f6f9a8c0; ?>
<?php unset($__attributesOriginal7274efa5ddca416a8c7d5935f6f9a8c0); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7274efa5ddca416a8c7d5935f6f9a8c0)): ?>
<?php $component = $__componentOriginal7274efa5ddca416a8c7d5935f6f9a8c0; ?>
<?php unset($__componentOriginal7274efa5ddca416a8c7d5935f6f9a8c0); ?>
<?php endif; ?>
                
            </div>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal3c8f9b7880ed8037e1c74e30535cdc4c)): ?>
<?php $attributes = $__attributesOriginal3c8f9b7880ed8037e1c74e30535cdc4c; ?>
<?php unset($__attributesOriginal3c8f9b7880ed8037e1c74e30535cdc4c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal3c8f9b7880ed8037e1c74e30535cdc4c)): ?>
<?php $component = $__componentOriginal3c8f9b7880ed8037e1c74e30535cdc4c; ?>
<?php unset($__componentOriginal3c8f9b7880ed8037e1c74e30535cdc4c); ?>
<?php endif; ?>
    </form>

</div>
<?php $__env->startSection('styles'); ?>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <style>
        .select2-container--default .select2-selection--single {
            display: flex;
            align-items: center;
            height: 38px;
            /* match your input height */
        }

        .select2-selection__rendered {
            line-height: 1.6 !important;
        }

        .form-group-box {
            border: 3px solid #dff9ff;
            padding: 1rem;
            border-radius: 6px;
            margin-bottom: 1.5rem;
        }

        .legend {
            font-weight: bold;
            font-size: 1rem !important;
        }

        .dropdown-menu {
            position: absolute !important;
            top: 100% !important;
            left: 0 !important;
            z-index: 1000 !important;
            width: 100% !important;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-top: none;
            border-radius: 0 0 0.25rem 0.25rem;
        }

        .dropdown-item {
            padding: 0.5rem 1rem;
            cursor: pointer;
        }

        .dropdown-item:hover {
            background-color: #f8f9fa;
        }

        .dropdown-item small {
            display: block;
            margin-top: 0.25rem;
        }

        .dropdown-item.active,
        .dropdown-item:active {
            background-color: #dff9ff;
            color: #000;
        }
        /* Select2 error styling - only when underlying select has is-invalid class */
        .is-invalid + .select2-container--default .select2-selection--single {
            border-color: #dc3545 !important; /* Bootstrap danger color */
        }

        .is-invalid + .select2-container--default.select2-container--focus .select2-selection--single,
        .is-invalid + .select2-container--default.select2-container--open .select2-selection--single {
            border-color: #dc3545 !important;
            box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
        }

    </style>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('scripts'); ?>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script>
        const comment = $('#comment').val();
        const operatorName = $('#operator_name').val();
        const operatorEmail = $('#operator_email').val();

        if (comment) {
            // Display comment in red alert container
            let commentHtml = `<div class="alert alert-danger mb-3 p-3">
                          <div class="w-100 text-break" style="word-break: break-word;">
                            <strong>Comment:</strong> ${comment}
                          </div>
                        </div>`;

            // Add operator information below without container if available
            if (operatorName && operatorEmail) {
                commentHtml += `<div class="text-right mb-3">
                          <span><strong>${operatorName}</strong> - ${operatorEmail}</span>
                        </div>`;
            }

            $('.card-header').html(commentHtml);
        }

        // this is to update the state selection when the state is changed from the select dropdown
        // Function to show dropdown for a specific field
        function showDropdown(field) {
            // Hide all dropdowns first
            document.querySelectorAll('.dropdown-menu').forEach(dropdown => {
                dropdown.style.display = 'none';
            });

            // Show the selected dropdown
            const dropdown = document.getElementById(field + '_dropdown');
            if (dropdown) {
                dropdown.style.display = 'block';
            }
        }

        function initializeFlatpickrModals() {
            // Initialize Flatpickr for Script Date input with future date restriction
            if (document.getElementById('script_date')) {
                const scriptDateInput = document.getElementById('script_date');
                const existingValue = scriptDateInput.value;

                flatpickr('#script_date', {
                    dateFormat: "m-d-Y",
                    allowInput: true,
                    clickOpens: true,
                    maxDate: "today",
                    defaultDate: existingValue && existingValue.trim() !== '' ? null : "today",
                    onReady: function(selectedDates, dateStr, instance) {
                        // Only set default to today if there's no existing value
                        if (!existingValue || existingValue.trim() === '') {
                            instance.setDate(new Date(), false);
                        }
                    }
                });
            }

            // Initialize Flatpickr for DOB modal input with future date restriction
            if (document.getElementById('dob_individual_date_input')) {
                const dobInput = document.getElementById('dob_individual_date_input');
                const existingValue = dobInput.value;

                flatpickr('#dob_individual_date_input', {
                    dateFormat: "m-d-Y",
                    allowInput: true,
                    clickOpens: true,
                    maxDate: new Date(Date.now() - 24 * 60 * 60 * 1000), // Yesterday
                    onReady: function(selectedDates, dateStr, instance) {
                        // Only set default year/month if there's no existing value
                        if (!existingValue || existingValue.trim() === '') {
                            instance.currentYear = 1990;
                            instance.currentMonth = 0; // January
                            instance.redraw();
                        }
                    },
                    onOpen: function(selectedDates, dateStr, instance) {
                        // Always reset to 1990 when opening if no value is selected
                        if (!instance.input.value || instance.input.value.trim() === '') {
                            instance.currentYear = 1990;
                            instance.currentMonth = 0; // January
                            instance.redraw();
                        }
                    }
                });
            }
        }

        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            if (!event.target.closest('.input-group')) {
                document.querySelectorAll('.dropdown-menu').forEach(dropdown => {
                    dropdown.style.display = 'none';
                });
            }
        });

        // Close dropdown on escape key
        // document.addEventListener('keydown', function(event) {
        //     if (event.key === 'Escape') {
        //         document.querySelectorAll('.dropdown-menu').forEach(dropdown => {
        //             dropdown.style.display = 'none';
        //         });
        //     }
        // });

        document.addEventListener('DOMContentLoaded', function() {
            window.addEventListener('dropdown-changed', event => {
                $('#state').val(event.detail.state).trigger('change');
                $('#gender').val(event.detail.gender).trigger('change');
            });

            window.addEventListener('template-dropdown-changed', event => {
                $('#medication').val(event.detail.medication).trigger('change');
            });
            initializeFlatpickrModals();
        });

        $('#state').on('change', function(e) {
            let selected = $(this).find('option:selected');
            let state = {
                id: selected.data('id'),
                name: selected.val(),
            };

            // Send the whole object to Livewire
            window.livewire.find('<?php echo e($_instance->id); ?>').getStateId(state);
        });

        $(document).ready(function() {
            $('#template').select2({
                placeholder: "Select Template",
            }).on('change', function(e) {
                window.livewire.find('<?php echo e($_instance->id); ?>').set('selectedTemplate', $(e.target).val());
            });

            $('#state').select2({
                placeholder: "Select State",
            }).on('change', function(e) {
                window.livewire.find('<?php echo e($_instance->id); ?>').set('importFile.state', $(e.target).val());
            });

            $('#gender').select2({
                placeholder: "Select Gender",
            }).on('change', function(e) {
                window.livewire.find('<?php echo e($_instance->id); ?>').set('importFile.gender', $(e.target).val());
            });

            $('#medication').select2({
                placeholder: "Select Medication",
            }).on('change', function(e) {
                window.livewire.find('<?php echo e($_instance->id); ?>').set('importFile.medication', $(e.target).val());
            });

            $('#ship_to').select2({
                placeholder: "Select Ship To",
            }).on('change', function(e) {
                window.livewire.find('<?php echo e($_instance->id); ?>').set('importFile.ship_to', $(e.target).val());
            });

            // Set initial value for ship_to if not already set
            if (!window.livewire.find('<?php echo e($_instance->id); ?>').get('importFile.ship_to')) {
                window.livewire.find('<?php echo e($_instance->id); ?>').set('importFile.ship_to', 'patient');
                $('#ship_to').val('patient').trigger('change');
            }
        });
    </script>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\KodeCreators\newlife-panel\resources\views/livewire/new-import.blade.php ENDPATH**/ ?>