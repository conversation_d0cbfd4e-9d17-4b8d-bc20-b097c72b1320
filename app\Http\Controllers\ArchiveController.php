<?php

namespace App\Http\Controllers;

use App\Events\ScriptStatusChanged;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\GetDatatableRequest;
use App\Http\Resources\DataTableCollection;
use App\Models\FaxNumbers;
use App\Models\Import;
use App\Models\ImportFile;
use App\Models\Medication;
use App\Models\State;
use App\Models\User;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Storage;
use App\Traits\FaxManager;
use App\Traits\FileManager;
use App\Services\LogService;
use Exception;
use Illuminate\Support\Facades\DB;
use App\Traits\DispenseProManager;
use Illuminate\Support\Str;


class ArchiveController extends Controller
{
    use FaxManager, FileManager;
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $page_title = 'Archive';

        return view('archive.index', [
            'page_title' => $page_title,
        ]);
    }

    public function preview(Request $request)
    {
        // Get the uploaded file
        $file = ImportFile::where('import_id', $request->importId)->with('import')->get();

        if (!$file) {
            $message = __('messages.script_not_found');

            session()->flash('error-message', $message);
            return back();
        }

        $has_back = request('return_url') ?: route('archive.index');
        $page_title = 'Data Preview';

        // Return the preview view
        return view('archive.preview', [
            'page_title' => $page_title,
            'has_back' => $has_back,
            'allRowsData' => $file,
        ]);
    }

    public function indexWeb(GetDatatableRequest $request)
    {
        $user = Auth::user();

        // Filter imports to only show those belonging to the current user
        $data = Import::query()->where('user_id', $user->id);
        $search = $request->input('query.search', '');
        $sort_order = $request->input('sort.sort', '');
        $sort_field = $request->input('sort.field', '');
        if ($search) {
            $query_search = "%" . $search . "%";

            $data->where(function ($query) use ($query_search) {
                $query->where('file_name', 'like', $query_search);
            });
        }

        if ($sort_order && $sort_field) {
            $importColumns = Schema::getColumnListing((new Import())->table);

            if (in_array($sort_field, $importColumns)) {
                $data->orderBy($sort_field, $sort_order);
            } else {
                // Default to latest if field doesn't exist
                $data->latest();
            }
        } else {
            $data->latest();
        }
        return new DataTableCollection($data->paginate($request->pagination['perpage'], ['*'], 'page', $request->pagination['page']));
    }

    public function downloadAllPdf($importId = null)
    {
        // Get the Import record
        $import = $importId ? Import::find($importId) : Import::latest()->first();

        if (!$import) {

            $message = __('messages.import_record_not_found');

            session()->flash('error-message', $message);
            return back();
        }

        // Determine the current status based on the request or route
        $currentRoute = request()->route()->getName();

        // Get ImportFile records for this Import filtered by status
        $query = ImportFile::where('import_id', $import->id);

        // Get the IDs of records currently displayed in the table
        // This ensures we download exactly what's shown in the UI
        $displayedIds = request()->input('displayed_ids');
        $allWithStatus = request()->input('all_with_status');

        // Log the request details for debugging
        Log::info('Download All PDF Request', [
            'displayed_ids' => $displayedIds,
            'all_with_status' => $allWithStatus,
            'import_id' => $importId,
            'route' => $currentRoute,
            'request_method' => request()->method(),
            'all_request_params' => request()->all()
        ]);

        // Get the current query SQL for debugging
        $initialSql = $query->toSql();
        $initialBindings = $query->getBindings();
        Log::info('Initial query before filtering by IDs', [
            'sql' => $initialSql,
            'bindings' => $initialBindings
        ]);

        // Count how many records would be returned without ID filtering
        $countBeforeIdFiltering = (clone $query)->count();
        Log::info('Record count before ID filtering', [
            'count' => $countBeforeIdFiltering
        ]);

        // If we have specific IDs, use them
        if ($displayedIds && is_array($displayedIds) && !empty($displayedIds)) {
            // Convert string IDs to integers if needed
            $numericIds = array_map(function ($id) {
                return is_numeric($id) ? (int)$id : $id;
            }, $displayedIds);

            // Only include the specific IDs provided
            $query->whereIn('id', $numericIds);

            Log::info('Filtering by displayed IDs', [
                'count' => count($numericIds),
                'ids' => $numericIds
            ]);

            // Get the updated query SQL for debugging
            $filteredSql = $query->toSql();
            $filteredBindings = $query->getBindings();
            Log::info('Query after filtering by IDs', [
                'sql' => $filteredSql,
                'bindings' => $filteredBindings
            ]);
        }

        // Order by ID to ensure consistent ordering
        $query->orderBy('id', 'desc');

        $importFiles = $query->get();

        // Log the final results
        Log::info('Files that will be included in the ZIP', [
            'count' => $importFiles->count(),
            'file_ids' => $importFiles->pluck('id')->toArray(),
            'file_names' => $importFiles->pluck('file_name')->toArray()
        ]);

        // Check if we have any files to process
        if ($importFiles->isEmpty()) {
            Log::warning('No files found matching the criteria', [
                'import_id' => $importId,
                'displayed_ids' => $displayedIds,
                'all_with_status' => $allWithStatus
            ]);
        }

        // Create a temporary directory to store PDFs
        $tempDir = storage_path('app/temp/prescriptions_' . time());
        if (!file_exists($tempDir)) {
            mkdir($tempDir, 0755, true);
        }

        // Copy each file to the temp directory
        foreach ($importFiles as $importFile) {
            $fileContent = null;

            // Check if file exists on public disk first, then try default disk
            if (Storage::disk('public')->exists($importFile->file_path)) {
                $fileContent = Storage::disk('public')->get($importFile->file_path);
            } elseif (Storage::exists($importFile->file_path)) {
                $fileContent = Storage::get($importFile->file_path);
            }

            if ($fileContent) {
                // Create a more descriptive filename based on patient data
                // Include the number field to make the filename more meaningful to the user
                $firstName = isset($importFile->first_name)
                    ? substr(preg_replace('/\s+/', '', $importFile->first_name), 0, 5)
                    : '';

                $lastName = isset($importFile->last_name)
                    ? substr(preg_replace('/\s+/', '', $importFile->last_name), 0, 5)
                    : '';

                $fileName = 'prescription_' .
                    ($importFile->number ? 'row' . $importFile->number . '_' : '') .
                    $importFile->id . '_' .
                    $lastName . '_' .
                    $firstName . '.pdf';


                // Replace spaces with underscores and remove any special characters
                $fileName = str_replace(' ', '_', $fileName);
                $fileName = preg_replace('/[^A-Za-z0-9\._-]/', '', $fileName);

                file_put_contents($tempDir . '/' . $fileName, $fileContent);
            }
        }

        // Create a ZIP file
        $zipFileName = 'prescriptions_' . date('Y-m-d') . '.zip';
        $zipFilePath = storage_path('app/temp/' . $zipFileName);

        // Ensure temp directory exists
        $tempZipDir = dirname($zipFilePath);
        if (!file_exists($tempZipDir)) {
            mkdir($tempZipDir, 0755, true);
        }

        $zip = new \ZipArchive();
        $zipResult = $zip->open($zipFilePath, \ZipArchive::CREATE | \ZipArchive::OVERWRITE);

        if ($zipResult === TRUE) {
            // Check if temp directory has files
            if (is_dir($tempDir) && count(glob("$tempDir/*")) > 0) {
                // Add files to the zip
                $files = new \RecursiveIteratorIterator(
                    new \RecursiveDirectoryIterator($tempDir),
                    \RecursiveIteratorIterator::LEAVES_ONLY
                );

                foreach ($files as $file) {
                    if (!$file->isDir()) {
                        $filePath = $file->getRealPath();
                        // Use a more unique naming scheme to prevent filename collisions
                        // Include the full path hash to ensure uniqueness
                        $pathHash = substr(md5($filePath), 0, 8);
                        $relativePath = $pathHash . '_' . basename($filePath);
                        $zip->addFile($filePath, $relativePath);
                    }
                }
            }

            $zip->close();

            // Verify ZIP file was created successfully
            if (file_exists($zipFilePath) && filesize($zipFilePath) > 0) {
                // Clean up the temporary directory
                if (is_dir($tempDir)) {
                    array_map('unlink', glob("$tempDir/*.*"));
                    rmdir($tempDir);
                }

                // Log bulk download
                LogService::logScriptDownloaded([
                    'patient_name' => 'Multiple scripts',
                    'medication' => 'Multiple medications',
                    'script_count' => $importFiles->count(),
                    'import_id' => $import->id
                ], 'bulk');

                // Return the ZIP file for download
                return response()->download($zipFilePath, $zipFileName, [
                    'Content-Type' => 'application/zip'
                ])->deleteFileAfterSend(true);
            } else {
                // Clean up failed ZIP attempt
                if (file_exists($zipFilePath)) {
                    unlink($zipFilePath);
                }
            }
        } else {
            Log::error('Failed to create ZIP file', [
                'zip_path' => $zipFilePath,
                'zip_result' => $zipResult,
                'temp_dir' => $tempDir
            ]);
        }

        // Clean up temporary directory if it still exists
        if (is_dir($tempDir)) {
            array_map('unlink', glob("$tempDir/*.*"));
            rmdir($tempDir);
        }

        $message = __('messages.zip_create_error');
        session()->flash('error-message', $message);

        return back();
    }

    public function download($id)
    {
        // If ID is provided, get the specific ImportFile
        if ($id !== null) {
            $importFile = ImportFile::find($id);
            if ($importFile) {
                // Check if file exists in storage (public disk first, then default)
                if (Storage::disk('public')->exists($importFile->file_path) || Storage::exists($importFile->file_path)) {
                    // Create a more descriptive filename based on patient data
                    $firstName = isset($importFile->first_name)
                        ? substr(preg_replace('/\s+/', '', $importFile->first_name), 0, 5)
                        : '';

                    $lastName = isset($importFile->last_name)
                        ? substr(preg_replace('/\s+/', '', $importFile->last_name), 0, 5)
                        : '';

                    $fileName = 'prescription_' . $importFile->id . '_' . $lastName . '_' . $firstName . '.pdf';


                    // Replace spaces with underscores and remove any special characters
                    $fileName = str_replace(' ', '_', $fileName);
                    $fileName = preg_replace('/[^A-Za-z0-9\._-]/', '', $fileName);

                    // Log script download
                    LogService::logScriptDownloaded([
                        'patient_name' => $importFile->first_name . ' ' . $importFile->last_name,
                        'medication' => $importFile->medication,
                        'script_id' => $importFile->id,
                        'status' => $importFile->status
                    ], 'single');

                    // Use the appropriate disk for download
                    if (Storage::disk('public')->exists($importFile->file_path)) {
                        return response()->download(storage_path('app/public/' . $importFile->file_path), $fileName);
                    } else {
                        return Storage::download($importFile->file_path, $fileName);
                    }
                }
            }
        }

        // If no valid ID or file not found, try to get the first ImportFile from the latest Import
        $latestImport = Import::latest()->first();
        if ($latestImport) {
            $firstFile = $latestImport->files()->first();
            if ($firstFile && (Storage::disk('public')->exists($firstFile->file_path) || Storage::exists($firstFile->file_path))) {
                // Create a more descriptive filename based on patient data
                $firstName = isset($importFile->first_name)
                    ? substr(preg_replace('/\s+/', '', $importFile->first_name), 0, 5)
                    : '';

                $lastName = isset($importFile->last_name)
                    ? substr(preg_replace('/\s+/', '', $importFile->last_name), 0, 5)
                    : '';

                $fileName = 'prescription_' . $importFile->id . '_' . $lastName . '_' . $firstName . '.pdf';


                // Replace spaces with underscores and remove any special characters
                $fileName = str_replace(' ', '_', $fileName);
                $fileName = preg_replace('/[^A-Za-z0-9\._-]/', '', $fileName);

                // Use the appropriate disk for download
                if (Storage::disk('public')->exists($firstFile->file_path)) {
                    return response()->download(storage_path('app/public/' . $firstFile->file_path), $fileName);
                } else {
                    return Storage::download($firstFile->file_path, $fileName);
                }
            }
        }

        // If all else fails, generate a PDF from session data (fallback)
        $allData = session('all_prescription_data', []);
        if (!empty($allData)) {
            $data = $allData[0] ?? [];
            $fileName = 'prescription.pdf';

            // Get the logged-in user
            $user = Auth::user();
            $userState = null;
            $doctorName = 'Dr. April'; // Default name

            if ($user) {
                // Get the user's full name for the signature
                $doctorName = $user->printed_name ?? ($user->first_name . ' ' . $user->last_name);

                // Get the user's state information if available
                if ($user->state_id) {
                    $userState = State::find($user->state_id);
                }
            }

            // Generate PDF
            $pdf = PDF::loadView('pdf.prescription', [
                'data' => $data,
                'isPdfDownload' => true,
                'user' => $user,
                'userState' => $userState,
                'doctorName' => $doctorName,
                'isSigned' => $importFile && $importFile->status === ImportFile::STATUS_PENDING_APPROVAL,
                'signed_at' => $importFile && $importFile->signed_at ? Carbon::parse($importFile->signed_at)->format('m/d/Y h:i A') : now()->format('m/d/Y h:i A'),
                'ip_address' => request()->ip(),
            ]);
            $pdf->setPaper('letter');

            return $pdf->download($fileName);
        }
        // No data available
        $message = __('messages.script_not_found');

        session()->flash('success-message', $message);

        return back();
    }

    public function show($id)
    {
        if ($id !== null) {
            $importFile = ImportFile::find($id);

            if ($importFile) {
                // Check if file exists on public disk first, then try default disk
                if (Storage::disk('public')->exists($importFile->file_path)) {
                    return response()->file(storage_path('app/public/' . $importFile->file_path));
                } elseif (Storage::exists($importFile->file_path)) {
                    return Storage::response($importFile->file_path);
                }
            }
        }

        $latestImport = Import::latest()->first();
        if ($latestImport) {
            $firstFile = $latestImport->files()->first();
            if ($firstFile) {
                // Check if file exists on public disk first, then try default disk
                if (Storage::disk('public')->exists($firstFile->file_path)) {
                    return response()->file(storage_path('app/public/' . $firstFile->file_path));
                } elseif (Storage::exists($firstFile->file_path)) {
                    return Storage::response($firstFile->file_path);
                }
            }
        }

        $allData = session('all_prescription_data', []);
        if (!empty($allData)) {
            $data = $allData[0] ?? [];

            $user = Auth::user();
            $userState = null;
            $doctorName = 'Dr. April';

            if ($user) {
                $doctorName = $user->printed_name ?? ($user->first_name . ' ' . $user->last_name);
                if ($user->state_id) {
                    $userState = State::find($user->state_id);
                }
            }

            $pdf = PDF::loadView('pdf.prescription', [
                'data' => $data,
                'isPdfDownload' => false,
                'user' => $user,
                'userState' => $userState,
                'doctorName' => $doctorName,
                'isSigned' => true,
                'userSignature' => $user->signature ? asset('storage/' . $user->signature) : null,
                'signed_at' => $importFile && $importFile->signed_at ? Carbon::parse($importFile->signed_at)->format('m/d/Y h:i A') : now()->format('m/d/Y h:i A'),
                'ip_address' => request()->ip(),
            ])->setPaper('letter');

            // Stream the PDF in the browser
            return $pdf->stream('prescription_preview.pdf');
        }
        $message = __('messages.script_not_found');

        session()->flash('error-message', $message);

        return back();
    }

    public function sign($id)
    {
        $importFile = ImportFile::find($id);
        if (!$importFile) {

            session()->flash('error-message', __('messages.script_not_found'));

            return back();
        }

        $import = $importFile->import;

        // Delete old PDF if it exists
        if (Storage::exists($importFile->file_path)) {
            Storage::delete($importFile->file_path);
        }

        // Get user data for the PDF
        $user = User::find($importFile->import->user_id);
        $userState = null;
        $doctorName = 'Dr. April';

        if ($user) {
            $doctorName = $user->printed_name ?? ($user->first_name . ' ' . $user->last_name);
            if ($user->state_id) {
                $userState = State::find($user->state_id);
            }
        }

        $data = [];

        // Format script_date in DD/MM/YYYY format
        $data[] = $importFile->script_date ? Carbon::parse($importFile->script_date)->format('m/d/Y') : '';
        $data[] = $importFile->last_name;
        $data[] = $importFile->first_name ?? '';
        // Format dob in DD/MM/YYYY format
        $data[] = $importFile->dob ? Carbon::parse($importFile->dob)->format('m/d/Y') : '';
        $data[] = $importFile->gender;
        $data[] = $importFile->address;
        $data[] = $importFile->city;
        $data[] = $importFile->state;
        $data[] = $importFile->zip;
        $data[] = $importFile->phone;
        $data[] = $importFile->medication;
        // $data[] = $importFile->stregnth;
        // $data[] = $importFile->dosing;
        $data[] = $importFile->refills;
        $data[] = $importFile->vial_quantity;
        $data[] = $importFile->days_supply;
        $data[] = $importFile->sig ?? '';
        $data[] = $importFile->notes ?? '';
        $data[] = $importFile->ship_to ?? '';

        $signatureImagePath = null;
        if ($user && $user->signature) {
            // Convert storage path to absolute filesystem path
            $signatureImagePath = storage_path('app/public/' . $user->signature);

            // Ensure file exists
            if (!file_exists($signatureImagePath)) {
                $signatureImagePath = null;
            }
        }

        // Get the client timestamp or use device time from session
        $signedAt = null;
        $clientTimestamp = request()->input('client_timestamp');

        if ($clientTimestamp) {
            try {
                // Parse the timestamp with timezone information
                $signedAt = Carbon::createFromFormat('Y-m-d H:i:s', $clientTimestamp);
                Log::info('Using client timestamp for signed_at in archive', [
                    'timestamp' => $clientTimestamp
                ]);
            } catch (\Exception $e) {
                Log::error('Failed to parse client timestamp in archive', [
                    'timestamp' => $clientTimestamp,
                    'error' => $e->getMessage()
                ]);
                // Fall through to try session timestamp or current time
                $this->tryUseSessionOrCurrentTime($signedAt);
            }
        } else {
            // No client timestamp in request, try session or current time
            $this->tryUseSessionOrCurrentTime($signedAt);
        }

        // Update status and signed_at timestamp
        // $importFile->status = ImportFile::STATUS_SIGNED;
        $importFile->status = ImportFile::STATUS_PENDING_APPROVAL;
        $importFile->signed_at = $signedAt;
        $importFile->save();

        // Dispatch the ScriptStatusChanged event
        $currentUser = Auth::user();
        if ($currentUser) {
            event(new ScriptStatusChanged([$importFile->toArray()], $currentUser));
            Log::info('ScriptStatusChanged event dispatched from ArchiveController', [
                'user_id' => $currentUser->id,
                'user_name' => $currentUser->first_name . ' ' . $currentUser->last_name,
                'import_file_id' => $importFile->id
            ]);
        }

        // Generate new PDF with signature - now that we have the signed_at timestamp saved
        // Format the signed_at timestamp
        $formattedSignedAt = $importFile->signed_at ? Carbon::parse($importFile->signed_at)->format('m/d/Y h:i A') : now()->format('m/d/Y h:i A');

        // Log the timestamp for debugging
        Log::info('Generating PDF with signed_at timestamp', [
            'import_file_id' => $importFile->id,
            'signed_at_db' => $importFile->signed_at,
            'formatted_signed_at' => $formattedSignedAt
        ]);

        $pdf = PDF::loadView('pdf.new-prescription', [
            'data' => $data,
            'isPdfDownload' => true,
            'user' => $user,
            'userState' => $userState,
            'doctorName' => $doctorName,
            'isSigned' => true,
            'userSignature' => $signatureImagePath,
            'signed_at' => $formattedSignedAt,
            'ip_address' => request()->ip(),
        ]);

        $pdf->setOption('isPhpEnabled', true);
        $pdf->setOption('isHtml5ParserEnabled', true);
        $pdf->setOption('isRemoteEnabled', false);
        $pdf->setPaper('letter');

        // Save the new PDF to the same path
        Storage::put($importFile->file_path, $pdf->output());

        $message = __('messages.script_signed_sent');

        session()->flash('success-message', $message);
        return back();
    }

    /**
     * Helper method to try using device time from session or current server time
     *
     * @param Carbon|null &$signedAt Reference to the signedAt variable to update
     * @return void
     */
    private function tryUseSessionOrCurrentTime(&$signedAt)
    {
        // Try to get device time from session
        $deviceTime = session('device_time');

        if ($deviceTime) {
            try {
                // Parse the timestamp with timezone information
                $signedAt = Carbon::createFromFormat('Y-m-d H:i:s O', $deviceTime);
                Log::info('Using device time from session for signed_at in archive', [
                    'timestamp' => $deviceTime
                ]);
            } catch (\Exception $e) {
                Log::error('Failed to parse device time from session in archive', [
                    'timestamp' => $deviceTime,
                    'error' => $e->getMessage()
                ]);
                // Fall through to use current time
                $signedAt = Carbon::now();
            }
        } else {
            // If no device time in session, use current server time
            $signedAt = Carbon::now();
            Log::info('Using current server time for signed_at in archive');
        }
    }

    public function sendFax($id, Request $request)
    {
        $dispatch_method = $request->query('dispatch_method');

        $importFile = ImportFile::find($id);
        if ($importFile->status == ImportFile::STATUS_PENDING_APPROVAL && !empty($importFile)) {

            if ($dispatch_method == User::DISPATCH_METHOD_FAX && $dispatch_method !== null) {
                try {
                    $response = FaxManager::uploadFiles($importFile->file_path);

                    if (isset($response['path'])) {
                        $faxFilePath = $response['path'];

                        $userId = config('fax.user_id');

                        $fax_numbers = FaxNumbers::where('is_active', 1)->get()->pluck('numbers')->toArray();
                        // Add + prefix to each fax number if not already present
                        $to = array_map(function ($number) {
                            return str_starts_with($number, '+') ? $number : '+' . $number;
                        }, $fax_numbers);

                        $from = config('fax.from_number');

                        try {
                            $sendFax = FaxManager::sendFax($to, [$faxFilePath], $from, $userId);

                            if ($sendFax) {
                                Log::info('Fax sent successfully', [
                                    'to' => $to,
                                    'from' => $from,
                                    'file' => $faxFilePath
                                ]);

                                // Log fax sent
                                LogService::logFaxSent([
                                    'patient_name' => $importFile->first_name . ' ' . $importFile->last_name,
                                    'medication' => $importFile->medication,
                                    'script_id' => $importFile->id,
                                    'status' => ImportFile::STATUS_SENT
                                ], implode(', ', $to), [
                                    'from' => $from,
                                    'file_path' => $faxFilePath
                                ]);

                                $importFile->status = ImportFile::STATUS_SENT;
                                $importFile->sent_at = Carbon::now();

                                $importFile->save();

                                $message = __('messages.fax_message_sent');
                                session()->flash('success-message', $message);

                                return back();
                            } else {
                                Log::error('Failed to send fax', [
                                    'to' => $to,
                                    'from' => $from,
                                    'file' => $faxFilePath
                                ]);
                                LogService::logFaxFailed('Failed to send fax', 'Fax API returned failure or no response.');

                                $message = __('messages.fax_sent_failed');
                                session()->flash('error-message', $message);

                                return back();
                            }
                        } catch (\App\Exceptions\CustomException $e) {
                            // Handle Fax API error response
                            Log::error('Fax API Error', [
                                'message' => $e->getMessage(),
                                'code' => $e->getCode()
                            ]);
                            LogService::logFaxFailed('Fax API Error', $e->getMessage()  . "\nStack Trace:\n" . $e->getTraceAsString());

                            // Parse the error message if it's in JSON format
                            $errorData = json_decode($e->getMessage(), true);
                            $errorMessage = 'Fax API Error: ';

                            if (is_array($errorData) && isset($errorData['description'])) {
                                // Format from the logs: {"description":"too_many_files","error":403}
                                $errorMessage .= $errorData['description'];
                            } else {
                                $errorMessage .= $e->getMessage();
                            }

                            session()->flash('error-message', $errorMessage);
                            return back();
                        } catch (\Exception $e) {
                            Log::error('Fax sending exception', [
                                'message' => $e->getMessage(),
                                'file' => $e->getFile(),
                                'line' => $e->getLine()
                            ]);
                            LogService::logFaxFailed('Fax sending exception', $e->getMessage() . "\nStack Trace:\n" . $e->getTraceAsString());

                            $message = __('messages.fax_api_error') . $e->getMessage();
                            session()->flash('error-message', $message);

                            return back();
                        }
                    } else {
                        $message = 'Unable to upload file to fax service. Please try again or contact support.';
                        session()->flash('error-message', $message);

                        return back();
                    }
                } catch (\App\Exceptions\CustomException $e) {
                    Log::error('Fax API Upload Error with Trace', [
                        'message' => $e->getMessage(),
                        'code' => $e->getCode(),
                        'trace' => $e->getTraceAsString() // Include the stack trace
                    ]);
                    LogService::logFaxFailed('Fax API Upload Error', $e->getMessage() . "\nStack Trace:\n" . $e->getTraceAsString()); // Include trace in LogService


                    // Parse the error message if it's in JSON format
                    $errorData = json_decode($e->getMessage(), true);
                    $errorMessage = __('messages.fax_api_error');

                    if (is_array($errorData) && isset($errorData['description'])) {
                        $errorMessage .= $errorData['description'];
                    } else {
                        $errorMessage .= $e->getMessage();
                    }

                    session()->flash('error-message', $errorMessage);

                    return back();
                } catch (\Exception $e) {
                    Log::error('File upload exception', [
                        'message' => $e->getMessage(),
                        'file' => $e->getFile(),
                        'line' => $e->getLine()
                    ]);
                    LogService::logFaxFailed('File upload exception', $e->getMessage() . "\nStack Trace:\n" . $e->getTraceAsString());

                    $message = 'Error uploading file: ' . $e->getMessage();
                    session()->flash('error-message', $message);

                    return back();
                }
            } else {
                $customer_dtls =  $importFile->import->user;
                $practice = null;

                if ($importFile->import->file_name == 'API Entry') {
                    $practice = $importFile->import->user->practice;
                }


                $state = State::where('short_name', $importFile->state)->first();
                $stateName = $state->name ?? null;

                $medication = Medication::where('name', $importFile->medication)->first();
                $ndc = $medication->ndc ?? null;

                $filePath = $importFile->file_path;
                // Check if file exists on public disk first, then try default disk
                if (Storage::disk('public')->exists($filePath)) {
                    $pdf = Storage::disk('public')->get($filePath);
                } elseif (Storage::exists($filePath)) {
                    $pdf = Storage::get($filePath);
                }

                if($ndc === null) {
                    LogService::NdcNotFound($importFile->medication, $importFile->id);
                    session()->flash('error-message', "NDC not found for medication: {$importFile->medication}");
                    return back();
                }

                if($stateName === null) {
                    LogService::StateNotFound($importFile->state, $importFile->id);
                    session()->flash('error-message', "State not found for state code: {$importFile->state}");
                    return back();
                }

                if (!$pdf) {
                    LogService::logPdfNotFound($importFile->id);
                    session()->flash('error-message', "PDF file not found or empty for file ID: {$importFile->id}");
                    return back();
                }

                $pdf = base64_encode($pdf);
                $orderId = $importFile->order_id ? $importFile->order_id : strtoupper(Str::random(3)) . rand(10000, 99999);

                $data = [
                    "action" => "order",
                    "orderId" => $orderId,
                    "shipTo" => $importFile->ship_to,
                    "privacyConsent" => "false",
                    "patient" => [
                        // "lastName" => "MAXLIFE",
                        // "firstName" => "TEST",
                        // TODO::uncomment below lines for production
                        "lastName" => $importFile->last_name,
                        "firstName" => $importFile->first_name,
                        "address" => $importFile->address,
                        "address2" => "",
                        "city" => $importFile->city,
                        "region" => $stateName,
                        "postalCode" => $importFile->zip,
                        "countryCode" => '',
                        "dob" => Carbon::parse($importFile->dob)->format('Ymd'),
                        "gender" => $importFile->gender,
                        "phone" => $importFile->phone,
                    ],
                    "customer" => [
                        "abbreviation" => $practice?->dispensepro_abbreviation ?? $customer_dtls->dispense_abbreviation,
                        "name" => $customer_dtls->printed_name,
                        "address" => $customer_dtls->address,
                        "address2" => "",
                        "city" => $customer_dtls->city,
                        "region" => $customer_dtls->state->name,
                        "postalCode" => $customer_dtls->zip,
                        "phone" => $customer_dtls->phone
                    ],
                    "hasPrescription" => "true",
                    "rxs" => [[
                        "note" => $importFile->notes,
                        "description" => $medication->dispensepro_medication_name,
                        "dose" => $importFile->dosing,
                        "quantity" => $importFile->vial_quantity,
                        "units" => "each",
                        "ndc" => $ndc,
                        "daysSupply" => $importFile->days_supply,
                        "sig" => $importFile->sig,
                        "writtenDate" => Carbon::parse($importFile->script_date)->format('Ymd'),
                        "prescriberNPI" => $customer_dtls->{"NPI#"},
                        "refills" => $importFile->refills,
                    ]],
                    "pdfScript" => $pdf
                ];

                $response = DispenseProManager::DispenseProCreateOrder($data);
                $response['orderId'] = $orderId;


                if ($response['status'] == 'ok') {

                    $importFile->order_id = $orderId;
                    $importFile->status = ImportFile::STATUS_SENT;
                    $importFile->sent_at = Carbon::now();
                    $importFile->save();
                    Log::info("DispensePro order sent successfully for ImportFile ID: {$importFile->id}");

                    LogService::logDispenseProOrderCreated($response);

                    session()->flash('success-message', "DispensePro order sent successfully");

                    return back();
                } else {

                    Log::warning("DispensePro order failed for ImportFile ID: {$importFile->id}, reason: " . json_encode($response['errorMessages']));

                    LogService::logDispenseProOrderFailed(
                        is_array($response['errorMessages']) ? implode(', ', $response['errorMessages']) : $response['errorMessages'],
                        $response
                    );

                    session()->flash('error-message', "DispensePro order failed");

                    return back();
                }
            }
        }

        $message = __('messages.script_not_signed');
        session()->flash('error-message', $message);
        return back();
    }

    //Archive section for Staff users

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function staffIndex()
    {
        $page_title = 'Archive';
        $providers = User::where('role', User::ROLE_PROVIDER)->get();

        return view('archive_staff.index', [
            'page_title' => $page_title,
            'providers' => $providers,
        ]);
    }

    public function staffIndexWeb(GetDatatableRequest $request)
    {
        $user = Auth::user();

        // Filter imports to only show those belonging to the current user
        $data = Import::query()->with('user');
        $search = $request->input('query.search', '');
        $sort_order = $request->input('sort.sort', '');
        $sort_field = $request->input('sort.field', '');
        if ($search) {
            // $query_search = "%" . $search . "%";
            $searchWords = preg_split('/\s+/', trim($search));
            $query_search = '%' . implode('%', $searchWords) . '%'; // "medical clinic" => "%medical%clinic%"

            $data->where(function ($query) use ($query_search) {
                $query->where('file_name', 'like', $query_search)
                    ->orWhereRaw("DATE_FORMAT(created_at, '%m/%d/%Y') LIKE ?", [$query_search])
                    ->orWhereHas('user', function ($q) use ($query_search) {
                        $q->where(DB::raw("CONCAT(first_name, ' ', last_name)"), 'like', $query_search);
                    });
            });
        }

        // Apply provider filter if provided
        $providerId = $request->input('query.query.provider_id');
        if ($providerId && !empty($providerId)) {

            $data->where('user_id', $providerId);

            Log::info('Filtering by provider', ['provider_id' => $providerId]);
        }

        if ($sort_order && $sort_field) {
            $importColumns = Schema::getColumnListing((new Import())->table);

            if (in_array($sort_field, $importColumns)) {
                $data->orderBy($sort_field, $sort_order);
            } else {
                // Default to latest if field doesn't exist
                $data->latest();
            }
        } else {
            $data->latest();
        }
        return new DataTableCollection($data->paginate($request->pagination['perpage'], ['*'], 'page', $request->pagination['page'])
            ->through(function ($item) {

                // Add provider name
                $provider = $item->user ?? null;
                $item->provider_name = $provider ? ($provider->first_name . ' ' . $provider->last_name) : 'N/A';

                return $item;
            }));
    }

    public function staffPreview(Request $request)
    {
        // Get the uploaded file
        $file = ImportFile::where('import_id', $request->importId)->with('import')->get();

        if (!$file) {
            $message = __('messages.script_not_found');

            session()->flash('error-message', $message);
            return back();
        }

        $has_back = request('return_url') ?: route('archive_staff.index');
        $page_title = 'Data Preview';

        // Return the preview view
        return view('archive_staff.preview', [
            'page_title' => $page_title,
            'has_back' => $has_back,
            'allRowsData' => $file,
        ]);
    }
}
