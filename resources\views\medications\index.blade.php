@extends('master')

@section('content')
    <div class="card card-custom mb-5">

        <div class="card-body" x-data="{ showFilter: false }">

            <div class="row justify-content-between ">
                <div class="col-12 col-sm-8 col-md-6 col-lg-4 col-xl-3 mb-6">
                    <div class="input-icon">
                        <input type="text" class="form-control" placeholder="Search..." id="medications_search" />
                        <span>
                            <i class="flaticon2-search-1 text-muted"></i>
                        </span>
                    </div>
                </div>
                <div class="col-12 col-sm">
                    <a href="{{ route('medications.create') }}">
                        <button type="button" class="btn btn-primary" id="add-box-btn">
                            <i class="fa fa-plus"></i>
                            <span>Add Medication</span>
                        </button>
                    </a>
                </div>
            </div>

            <div class="datatable datatable-bordered datatable-head-custom" id="medications_dt"></div>
        </div>
    </div>
@endsection
@section('styles')
@endsection
@section('scripts')
    <script src="https://cdn.jsdelivr.net/gh/alpinejs/alpine@v2.7.0/dist/alpine.min.js" defer></script>
    <script>
        var datatable;
        var datatableElement;
        var searchElement;
        var columnArray;

        const storagePath = `{{ url('/storage') }}`;
        const apiRoute = `{{ route('medications.api') }}`;
        let url = "{{ Storage::url('/') }}";
        const deleteRoute = `{{ route('medications.delete', ['::ID']) }}`;
        const statusChangeRoute = `{{ route('medications.status', ['medication' => '::ID']) }}`;
        const editRoute = `{{ route('medications.edit', ['medication' => '::ID']) }}`;



        datatableElement = $('#medications_dt');
        searchElement = $('#medications_search');

        columnArray = [{
                field: 'name',
                title: `Medication Name`,
                width: 200,
                sortable: true,
                autoHide: false,
            },
            {
                field: 'ndc',
                title: `NDC Code`,
                width: 'auto',
                sortable: true,
                autoHide: false,
            },
            {
                field: 'created_at',
                title: 'Created At',
                width: 150,
                sortable: true,
                autoHide: false,
                template: function(data) {
                    return moment(data.created_at).format('MM/DD/YYYY');
                },
            },
            {
                field: 'is_active',
                title: 'active',
                width: 60,
                template: function(row) {
                    return `
                        <span class="switch switch-sm switch-primary">
                            <label>
                                <input type="checkbox" data-id="${row.id}" class="status-change" ${row.is_active ? 'checked' : ''} name="${row.id}"/>
                                <span></span>
                            </label>
                        </span>
                    `;
                }
            },
            {
                field: 'Actions',
                title: 'Actions',
                sortable: false,
                width: 'auto',
                overflow: 'visible',
                autoHide: false,
                template: function(data) {
                    return `
                            <a href="${editRoute.replace('::ID', data.id)}" class="btn btn-sm btn-clean btn-icon" data-toggle="tooltip" title="Edit Medication Details">
                                <i class="menu-icon fas fa-pen"></i>
                                   </a>

                                   <a data-id="${data.id}" onclick="deleteRequest(this)" class="deleteRequest btn btn-sm btn-clean btn-icon" data-toggle="tooltip" title="Delete Medication">
                        <i class="menu-icon fas fa-trash"></i>
                           </a>

                            `;


                },
            }
        ];

        datatable = datatableElement.KTDatatable({
            data: {
                type: 'remote',
                source: {
                    read: {
                        url: apiRoute,
                        //sample custom headers
                        headers: {
                            'X-CSRF-TOKEN': '{{ csrf_token() }}'
                        },
                        map: function(raw) {
                            // sample data mapping
                            var dataSet = raw;
                            if (typeof raw.data !== 'undefined') {
                                dataSet = raw.data;
                            }
                            return dataSet;
                        },
                    },
                },
                pageSize: 10,
                serverPaging: true,
                serverFiltering: true,
                serverSorting: true,
            },
            pagination: true,
            search: {
                input: searchElement,
                key: 'search'
            },
            layout: {
                customScrollbar: false,
                scroll: true,
            },
            columns: columnArray
        });

        const routeTemplate = "{{ route('archive.download-all-pdf', ['importId' => '__ID__']) }}";

        // Handle download all button
        datatableElement.on('click', '#download-all-btn', function() {
            const importId = $(this).data('id');
            const url = routeTemplate.replace('__ID__', importId ?? '');

            const form = $('<form>', {
                method: 'POST',
                action: url
            });

            // Add CSRF token
            form.append($('<input>', {
                type: 'hidden',
                name: '_token',
                value: '{{ csrf_token() }}'
            }));

            // Submit the form
            $('body').append(form);
            form.submit();
            form.remove();
        });

        function deleteRequest(btn) {
            Swal.fire({
                title: 'Are you sure?',
                text: "You want to Delete this Medication?",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, Delete the Medication!'
            }).then((result) => {
                if (result.isConfirmed) {
                    $.ajax({
                        url: deleteRoute.replace('::ID', $(btn).data('id')),
                        method: 'POST',
                        headers: {
                            'X-CSRF-TOKEN': '{{ csrf_token() }}'
                        },
                        success: function(res) {
                            toastr.success(res.message);
                            datatable.reload();
                        }
                    });
                }
            });
        }
        datatableElement.on('click', '.status-change', function() {
            let id = $(this).data('id');
            let val = $(this).is(":checked") ? 1 : 0;
            $.ajax({
                    url: statusChangeRoute.replace('::ID', id),
                    headers: {
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                })
                .done(function(res) {
                    toastr.success(res.message);
                    datatable.reload();
                });
        });
    </script>
@endsection
