<?php

namespace App\Http\Livewire\Practice\Provider;

use App\Models\PracticeProvider;
use App\Models\User;
use Livewire\Component;

class CreateEdit extends Component
{
    public $providers = [];
    public $practice_id;
    public $provider_id;

    protected $rules = [
        'provider_id' => 'required|exists:users,id',
    ];

    public function messages()
    {
        return [
            'provider_id.required' => 'Provider is required',
            'provider_id.exists' => 'Selected provider does not exist',
        ];
    }

    public function mount()
    {
        $this->loadProviders();
    }

    public function loadProviders()
    {
        $this->providers = User::where('role', User::ROLE_PROVIDER)->whereNotIn('id', function ($query) {
            $query->select('provider_id')
                ->from('practice_providers')
                ->where('practice_id', $this->practice_id);
        })->get();
    }

    public function store()
    {
        $this->validate();

        $practice_provider = PracticeProvider::create([
            'practice_id' => $this->practice_id,
            'provider_id' => $this->provider_id,
        ]);

        $practice_provider->save();

        if ($practice_provider) {

            session()->flash('success-message', 'Provider added successfully');
        }

        return redirect()->route('practices.providers', $this->practice_id);
    }
    public function render()
    {
        return view('livewire.practice.provider.create-edit');
    }
}
