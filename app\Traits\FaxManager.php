<?php

namespace App\Traits;

use App\Exceptions\CustomException;
use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;

trait FaxManager
{
    /**
     * Sets up HTTP client with authentication token and base URL for fax operations
     *
     * @throws Exception If environment variables are not properly configured
     * @return \Illuminate\Http\Client\PendingRequest
     */
    static function setUpFaxHttpClient()
    {
        $token = config('fax.token');
        $base_url = config('fax.base_url');

        if (!$token || !$base_url) {
            throw new Exception("Please set up .env file");
        }
        $http = Http::withToken($token)->baseUrl($base_url);
        return $http;
    }

    /**
     * Retrieves all fax accounts from the system
     *
     * @throws CustomException When API returns non-200 status code
     * @throws Exception For other unexpected errors
     * @return array JSON response containing accounts data
     */
    static function getAccounts()
    {
        $http = FaxManager::setUpFaxHttpClient();
        try {
            $response = $http->get('/accounts');
            if ($response->status() != 200) {
                throw new CustomException($response->json()['message'], $response->status());
            }
        } catch (CustomException $e) {
            throw $e;
        } catch (\Throwable $th) {
            throw new Exception($th->getMessage());
        }
        return $response->json();
    }

    /**
     * Retrieves a specific fax account by user ID
     *
     * @param string|int $user_id The ID of the user account to retrieve
     * @throws CustomException When API returns non-200 status code
     * @throws Exception For other unexpected errors
     * @return array JSON response containing account data
     */
    static function getAccount($user_id)
    {
        $http = FaxManager::setUpFaxHttpClient();
        try {
            $response = $http->get("/accounts/$user_id");
            if ($response->status() != 200) {
                throw new CustomException($response->json()['message'], $response->status());
            }
        } catch (CustomException $e) {
            throw $e;
        } catch (\Throwable $th) {
            throw new Exception($th->getMessage());
        }
        return $response->json();
    }

    /**
     * Uploads files to a user's fax account
     *
     * @param array $files to upload file
     * @param string|int $userId The ID of the user account
     * @throws CustomException When API returns non-200 status code
     * @throws Exception For other unexpected errors
     * @return array JSON response containing upload results
     */
    static function uploadFiles($file)
    {
        $http = FaxManager::setUpFaxHttpClient();
        $userId = config('fax.user_id');

        try {
            // $fileContents = file_get_contents($file);
            $fileContents = Storage::get($file);
            if (!$fileContents) {
                throw new CustomException('Failed to read file contents.');
            }

            $response = $http->attach(
                'fax_file',                       // Form field name
                $fileContents,                    // File contents
                basename($file)     // File name
            )->post("/accounts/{$userId}/files");

            if ($response->successful() && $response->status() == 201) {
                return $response->json();
            }

            // If response is not successful, throw error
            $responseData = $response->json();
            $errorMessage = is_array($responseData) ? json_encode($responseData) : $responseData;
            throw new CustomException($errorMessage, $response->status());
        } catch (CustomException $e) {
            // Maybe log it if needed
            throw $e;
        } catch (\Throwable $th) {
            throw new Exception($th->getMessage());
        }
    }


    /**
     * Retrieves the outbox fax list for a specific user
     *
     * @param string|int $userId The ID of the user account
     * @throws CustomException When API returns non-200 status code
     * @throws Exception For other unexpected errors
     * @return array JSON response containing outbox fax list
     */
    static function getOutboxFaxList($userId)
    {
        $http = FaxManager::setUpFaxHttpClient();
        try {
            $response = $http->get("/accounts/{$userId}/outbox");
            if ($response->status() != 200) {
                throw new CustomException($response->json()['message'], $response->status());
            }
        } catch (CustomException $e) {
            throw $e;
        } catch (\Throwable $th) {
            throw new Exception($th->getMessage());
        }
        return $response->json();
    }

    /**
     * Sends a fax with specified parameters
     *
     * @param string|array $to Recipient fax number(s)
     * @param string|array $files Path(s) to the document(s) to be faxed
     * @param string $from Sender fax number
     * @param array $options Additional fax options
     * @param string|int $userId The ID of the user account
     * @throws CustomException When API returns non-200 status code
     * @throws Exception For other unexpected errors
     * @return array JSON response containing send fax results
     */
    static function sendFax($to, $files, $from, $userId, $options = [])
    {
        $http = FaxManager::setUpFaxHttpClient();
        // Ensure $to and $files are arrays
        $recipients = is_array($to) ? $to : [$to];
        $documents = is_array($files) ? $files : [$files];

        // Set default options
        $defaultOptions = [
            'enhancement' => true,
            'retry' => [
                'count' => 2,
                'delay' => 15
            ]
        ];

        // Merge default options with provided options
        $faxOptions = array_merge($defaultOptions, $options);
        try {
            $response = $http->post("/accounts/{$userId}/outbox", [
                'to' => $recipients,
                'files' => $documents,
                'from' => $from,
                'options' => $faxOptions
            ]);
            if ($response->status() == 201) {
                return $response->json();
            }

            // Get the response data
            $responseData = $response->json();

            // Check if 'message' key exists, otherwise use a default message or the entire response
            $errorMessage = isset($responseData['message'])
                ? $responseData['message']
                : 'Error sending fax. Status code: ' . $response->status();

            // Log the full response for debugging
            \Illuminate\Support\Facades\Log::error('Fax API Error Response', [
                'status' => $response->status(),
                'response' => $responseData
            ]);

            throw new CustomException($errorMessage, $response->status());
        } catch (CustomException $e) {
            throw $e;
        } catch (\Throwable $th) {
            throw new Exception($th->getMessage());
        }
    }

    /**
     * Deletes a fax from the outbox
     *
     * @param string|int $userId The ID of the user account
     * @param string|int $outbox_fax_id The ID of the fax to delete
     * @throws CustomException When API returns non-200 status code
     * @throws Exception For other unexpected errors
     * @return array JSON response containing deletion results
     */
    static function deleteOutboxFax($userId, $outbox_fax_id)
    {
        $http = FaxManager::setUpFaxHttpClient();
        try {
            $response = $http->delete("/accounts/{$userId}/outbox/{$outbox_fax_id}");
            if ($response->status() != 200) {
                throw new CustomException($response->json()['message'], $response->status());
            }
        } catch (CustomException $e) {
            throw $e;
        } catch (\Throwable $th) {
            throw new Exception($th->getMessage());
        }
        return $response->json();
    }

    /**
     * Retrieves the inbox fax list for a specific user
     *
     * @param string|int $userId The ID of the user account
     * @throws CustomException When API returns non-200 status code
     * @throws Exception For other unexpected errors
     * @return array JSON response containing inbox fax list
     */
    static function getInboxFaxList($userId)
    {
        $http = FaxManager::setUpFaxHttpClient();
        try {
            $response = $http->get("/accounts/{$userId}/faxes");
            if ($response->status() != 200) {
                throw new CustomException($response->json()['message'], $response->status());
            }
        } catch (CustomException $e) {
            throw $e;
        } catch (\Throwable $th) {
            throw new Exception($th->getMessage());
        }
        return $response->json();
    }
}
