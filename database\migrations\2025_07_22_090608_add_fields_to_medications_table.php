<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('medications', function (Blueprint $table) {
            $table->string('dispensepro_medication_name')->nullable();
            $table->string('vial_quantity')->nullable();
            $table->string('days_supply')->nullable();
            $table->text('sig')->nullable();
            $table->text('notes')->nullable();
            $table->string('refills')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('medications', function (Blueprint $table) {
            $table->dropColumn('dispensepro_medication_name');
            $table->dropColumn('vial_quantity');
            $table->dropColumn('days_supply');
            $table->dropColumn('sig');
            $table->dropColumn('notes');
            $table->dropColumn('refills');
        });
    }
};
