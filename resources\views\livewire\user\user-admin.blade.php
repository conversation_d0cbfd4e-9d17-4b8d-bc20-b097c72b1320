<div>
    <form wire:submit.prevent="store">
        <x-layout.row>
            <div class="col">
                <x-card.body>
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <x-form.input.text label="Email" labelRequired="1" model="user.email" />
                        </div>

                        <div class="col-md-12 mb-3">
                            <x-form.input.text label="First Name" labelRequired="1" model="user.first_name" />
                        </div>

                        <div class="col-md-12 mb-3">
                            <x-form.input.text label="Last Name" labelRequired="1" model="user.last_name" />
                        </div>
                        <div class="col-md-12 mb-3">
                            <x-form.input.drop-down label="Access Type" labelRequired="1" model="user.role">
                                <option value="">Select Access Type</option>
                                <option value="{{ \App\Models\User::ROLE_ADMIN }}"
                                    {{ $user->role == \App\Models\User::ROLE_ADMIN ? 'selected' : '' }}>Administrator
                                </option>
                                <option value="{{ \App\Models\User::ROLE_OPERATOR }}"
                                    {{ $user->role == \App\Models\User::ROLE_OPERATOR ? 'selected' : '' }}>Operator
                                </option>
                            </x-form.input.drop-down>
                        </div>

                    </div>
                </x-card.body>

                <x-group.errors />

                <x-card.footer>
                    <x-btn-with-loading target="store" text="Save" type="submit" />
                </x-card.footer>
            </div>
        </x-layout.row>
    </form>
</div>

@push('styles')
    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <style>
        .select2-container .select2-selection--single {
            height: 38px;
            display: flex;
            align-items: center;
            padding: 6px 12px;
        }

        .select2-container .select2-selection--single .select2-selection__arrow {
            height: 100%;
            display: flex;
            align-items: center;
            padding-left: 8px;
        }
    </style>
@endpush

@push('scripts')
    <!-- Select2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script>
        document.addEventListener("livewire:load", function() {
            function createRoleDropdown() {
                $('#user\\.role').select2({
                    placeholder: "Select Access Type",
                    width: '100%'
                    // Search is enabled by default; do not set minimumResultsForSearch
                }).on('change', function(e) {
                    @this.set('user.role', $(e.target).val());
                });
            }
            createRoleDropdown();
            // Re-initialize Select2 after Livewire updates the DOM
            window.livewire.on('contentChanged', function() {
                createRoleDropdown();
            });
        });
    </script>
@endpush
