@props([
    'label',
    'labelRequired' => false,
    'model',
    'placeholder',
    'helper' => false,
    'fullWidth' => false,
    'slot',
])

<x-form.group>
    <x-form.label label="{{ $label }}" labelRequired="{{ $labelRequired }}" for="{{ $model }}" />
    <div wire:ignore>
        {{-- <x-form.input model="{{ $model }}" id="{{ $model }}" placeholder="{{ $placeholder ?? ('Enter '.$label) }}" type="text" {{ $attributes }} /> --}}
        <select wire:model.debounce.300ms="{{ $model }}" name="{{ $model }}" id="{{ $model }}" data-width="100%" title="{{ $placeholder ?? ('Enter '.$label) }}" class="form-control">

            {{ $slot }}
        </select>
    </div>
    <x-form.error model="{{ $model }}" />
    <x-form.helper helper="{{ $helper }}" />
</x-form.group>

{{-- @push('scripts')
    <script>
        document.addEventListener("livewire:load", () => {
            $('#{{ str_replace(".","\\\.",$model) }}').datepicker({
                format: 'yyyy-mm-dd',
                autoclose: true,
            }).on('hide.datepicker', function(e) {
                @this.set("{{ $model }}", $(e.target).val());
            });
        });
    </script>
@endpush --}}
