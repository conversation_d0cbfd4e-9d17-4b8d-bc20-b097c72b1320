<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('webhook_data', function (Blueprint $table) {
            $table->foreignId('import_file_id')->nullable()->constrained('import_files')->cascadeOnDelete()->cascadeOnUpdate();
            $table->string('order_id')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('webhook_data', function (Blueprint $table) {
            $table->dropColumn('import_file_id');
            $table->dropColumn('order_id');
        });
    }
};
