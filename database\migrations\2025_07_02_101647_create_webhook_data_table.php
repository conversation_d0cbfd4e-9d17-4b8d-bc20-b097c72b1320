<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('webhook_data', function (Blueprint $table) {
            $table->id();
            $table->json('data')->nullable()->comment('The raw webhook data received');
            $table->string('ip_address', 45)->nullable()->comment('IP address of the sender');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('webhook_data');
    }
};
