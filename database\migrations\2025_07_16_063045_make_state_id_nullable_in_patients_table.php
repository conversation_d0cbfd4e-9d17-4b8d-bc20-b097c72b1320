<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('patients', function (Blueprint $table) {
            // Drop the foreign key constraint first
            $table->dropForeign(['state_id']);

            // Make the column nullable
            $table->unsignedBigInteger('state_id')->nullable()->change();

            // Re-add the foreign key constraint with cascade rules
            $table->foreign('state_id')
                ->references('id')
                ->on('states')
                ->cascadeOnDelete()
                ->cascadeOnUpdate();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('patients', function (Blueprint $table) {
            // Drop the foreign key constraint
            $table->dropForeign(['state_id']);

            // Make the column NOT NULL again
            $table->unsignedBigInteger('state_id')->nullable(false)->change();

            // Re-add the foreign key constraint
            $table->foreign('state_id')
                ->references('id')
                ->on('states')
                ->cascadeOnDelete()
                ->cascadeOnUpdate();
        });
    }
};
