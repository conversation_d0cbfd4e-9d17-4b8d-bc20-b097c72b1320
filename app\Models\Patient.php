<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Patient extends Model
{
    use HasFactory;

    //TABLE
    public $table = 'patients';

    //FILLABLES
    protected $fillable = ['provider_id', 'first_name', 'last_name', 'dob', 'gender', 'city', 'state_id', 'zip_code', 'phone_number', 'address'];

    //HIDDEN
    protected $hidden = [];

    //APPENDS
    protected $appends = [];

    //WITH
    protected $with = [];

    //CASTS
    protected $casts = [];

    //RULES
    public static $getListRules = [];

    //RELATIONSHIPS
    public function provider()
    {
        return $this->belongsTo(User::class, 'provider_id');
    }

    public function state()
    {
        return $this->belongsTo(State::class, 'state_id');
    }

    //ATTRIBUTES
    //public function getExampleAttribute()
    //{
    //    return $data;
    //}
}
