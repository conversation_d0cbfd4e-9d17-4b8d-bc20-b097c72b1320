<?php

return [

    //patient related messages
    'patient_deleted_successfully' => 'Patient deleted successfully.',
    //user related messages
    'practice_list_returned' => 'Practice list returned successfully.',
    'practice_created_successfully' => 'Practice created successfully.',
    'practice_token_generated_successfully' => 'Practice token generated successfully.',
    'practice_details_returned' => 'Practice details returned successfully.',

    //medication related messages
    'medication_list_returned' => 'Medication list returned successfully.',
    'order_created_successfully' => 'Order created successfully.',
    'void_successfully' => 'Order voided sucessfully',
    'medication_created_successfully' => 'Medication created successfully.',
    'medication_updated_successfully' => 'Medication updated successfully.',
    'medication_deleted_successfully' => 'Medication deleted successfully.',
    'order_id_not_found' => 'Order ID not found.',
    'script_not_in_sent_status' => 'Wait for sometime until the order is sent in DispensePro.',
    'specified_medication_not_found' => 'This practice does not have access to the specified medication.',
    'medication_not_found' => 'Medication not found with the provided name.',
    'specified_provider_not_found' => 'This practice does not have access to the specified provider.',
    'user_not_found_with_npi' => 'User not found with the provided NPI number.',

    //provider related messages
    'users_list_returned' => 'Users list returned successfully.',
    'user_created_successfully' => 'User created successfully.',
    'user_updated_successfully' => 'User updated successfully.',
    'user_deleted_successfully' => 'User deleted successfully.',
    'url_not_found' => 'URL not found.',

    //script related messages
    'scripts_list_returned' => 'Scripts list returned successfully.',
    'script_created_successfully' => 'Script created successfully.',
    'script_updated_successfully' => 'Script updated successfully.',
    'script_deleted_successfully' => 'Script deleted successfully.',
    'script_recalled_for_revision' => 'Script recalled for revision.',
    'script_returned_for_revision' => 'Script returned for revision.',
    'import_created_successfully' => 'Import created successfully.',
    'script_already_returned_for_revision' => 'Script already returned for revision.',
    'void_failed' => 'Order void failed. Something went wrong in DispensePro.',
];
