@extends('master')

@section('content')
    <div class="card card-custom mb-5">

        <div class="card-body" x-data="{ showFilter: false }">

            <div class="row justify-content-between ">
                <div class="col-12 col-sm-8 col-md-6 col-lg-4 col-xl-3 mb-6">
                    <div class="input-icon">
                        <input type="text" class="form-control" placeholder="Search..." id="users_search" />
                        <span>
                            <i class="flaticon2-search-1 text-muted"></i>
                        </span>
                    </div>
                </div>
                <div class="col-12 col-sm">
                    <a href="{{ route('users.create') }}">
                        <button type="button" class="btn btn-primary" id="add-box-btn">
                            <i class="fa fa-plus"></i>
                            <span>Add Provider</span>
                        </button>
                    </a>
                </div>

            </div>

            <div class="datatable datatable-bordered datatable-head-custom" id="users_dt"></div>

        </div>
    </div>
@endsection
@section('styles')
@endsection
@section('scripts')
    <script src="https://cdn.jsdelivr.net/gh/alpinejs/alpine@v2.7.0/dist/alpine.min.js" defer></script>
    <script>
        var datatable;
        var datatableElement;
        var searchElement;
        var columnArray;

        const storagePath = `{{ url('/storage') }}`;
        const apiRoute = `{{ route('users.api') }}`;
        const editRoute = `{{ route('users.edit', ['user' => '::ID']) }}`;
        const statusChangeRoute = `{{ route('users.status', ['user' => '::ID']) }}`;
        const deleteRoute = `{{ route('users.delete', ['::ID']) }}`;
        const resetPasswordRoute = `{{ route('users.send-temp-password') }}`;
        let url = "{{ Storage::url('/') }}";

        datatableElement = $('#users_dt');
        searchElement = $('#users_search');

        columnArray = [{
                field: 'first_name',
                title: `First Name`,
                width: 200,
                sortable: true,
                autoHide: false,
            },
            {
                field: 'last_name',
                title: `Last Name`,
                width: 200,
                sortable: true,
                autoHide: false,
            },
            {
                field: 'email',
                title: `email`,
                width: 200,
                sortable: true,
                autoHide: false,
            },
            {
                field: 'printed_name',
                title: `Printed Name`,
                width: 200,
                sortable: true,
                autoHide: false,
            },
            {
                field: 'clinic_name',
                title: `Clinic Name`,
                width: 200,
                sortable: true,
                autoHide: false,
                template: function(row) {
                    return row.clinic_name || '-';
                }
            },
            {
                field: 'default_dispatch_method',
                title: 'Dispatch Method',
                overflow: 'visible',
                autoHide: false,
                width: 200,
                template: function(row) {
                    return row.default_dispatch_method || '-';
                }
            },
            {
                field: 'is_active',
                title: 'active',
                overflow: 'visible',
                autoHide: false,
                width: 'auto',
                template: function(row) {
                    return `
                        <span class="switch switch-sm switch-primary">
                            <label>
                                <input type="checkbox" data-id="${row.id}" class="status-change" ${row.is_active ? '' : 'checked'} name="${row.id}"/>
                                <span></span>
                            </label>
                        </span>
                    `;
                }
            },
            {
                field: 'Actions',
                title: 'Actions',
                sortable: false,
                width: 'auto',
                overflow: 'visible',
                autoHide: false,
                template: function(data) {
                    return `
                            <a href="${editRoute.replace('::ID', data.id)}" class="btn btn-sm btn-clean btn-icon" data-toggle="tooltip" title="Edit Provider Details">
                                <i class="menu-icon fas fa-pen"></i>
                            </a>
                            <button type="button" class="btn btn-sm btn-clean btn-icon reset-password-btn"
                                data-toggle="tooltip" title="Reset Password" data-id="${data.id}" data-email="${data.email}">
                                <i class="menu-icon fas fa-key"></i>
                            </button>
                            `;
                },
            }
        ];

        datatable = datatableElement.KTDatatable({
            data: {
                type: 'remote',
                source: {
                    read: {
                        url: apiRoute,
                        //sample custom headers
                        headers: {
                            'X-CSRF-TOKEN': '{{ csrf_token() }}'
                        },
                        map: function(raw) {
                            // sample data mapping
                            var dataSet = raw;
                            if (typeof raw.data !== 'undefined') {
                                dataSet = raw.data;
                            }
                            return dataSet;
                        },
                    },
                },
                pageSize: 10,
                serverPaging: true,
                serverFiltering: true,
                serverSorting: true,
            },
            pagination: true,
            search: {
                input: searchElement,
                key: 'search'
            },
            layout: {
                customScrollbar: false,
                scroll: true,
            },
            columns: columnArray
        });

        function deleteRequest(btn) {
            Swal.fire({
                title: 'Are you sure?',
                text: "You want to Delete this User?",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, Delete the User!'
            }).then((result) => {
                if (result.isConfirmed) {
                    $.ajax({
                        url: deleteRoute.replace('::ID', $(btn).data('id')),
                        method: 'POST',
                        headers: {
                            'X-CSRF-TOKEN': '{{ csrf_token() }}'
                        },
                        success: function(res) {
                            toastr.success(res.message);
                            datatable.reload();
                        }
                    });
                }
            });
        }
        datatableElement.on('click', '.status-change', function() {
            let id = $(this).data('id');
            let val = $(this).is(":checked") ? 1 : 0;
            $.ajax({
                    url: statusChangeRoute.replace('::ID', id),
                    headers: {
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                })
                .done(function(res) {
                    toastr.success(res.message);
                    datatable.reload();
                });
        });

        // Reset Password Button Click Handler
        datatableElement.on('click', '.reset-password-btn', function() {
            let email = $(this).data('email');

            Swal.fire({
                title: 'Reset Password',
                text: "Are you sure you want to reset the password for " + email + "?",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, reset password!'
            }).then((result) => {
                if (result.isConfirmed) {
                    // Create loading overlay
                    const loadingOverlay = $('<div>', {
                        'class': 'loading-overlay',
                        'css': {
                            'position': 'fixed',
                            'top': 0,
                            'left': 0,
                            'width': '100%',
                            'height': '100%',
                            'background-color': 'rgba(0, 0, 0, 0.7)',
                            'z-index': 9999,
                            'display': 'flex',
                            'justify-content': 'center',
                            'align-items': 'center',
                            'flex-direction': 'column'
                        }
                    });

                    // Add spinner and text to the overlay
                    const spinner = $('<div>', {
                        'class': 'spinner-border text-light',
                        'role': 'status',
                        'css': {
                            'width': '3rem',
                            'height': '3rem'
                        }
                    });

                    const loadingText = $('<div>', {
                        'class': 'mt-3 text-light',
                        'text': 'Sending password reset email...'
                    });

                    loadingOverlay.append(spinner, loadingText);
                    $('body').append(loadingOverlay);

                    $.ajax({
                        url: resetPasswordRoute,
                        method: 'POST',
                        data: {
                            email: email
                        },
                        headers: {
                            'X-CSRF-TOKEN': '{{ csrf_token() }}'
                        },
                        success: function(res) {
                            // Remove loading overlay
                            $('.loading-overlay').remove();
                            toastr.success(
                                'A temporary password has been sent to the user\'s email.');
                        },
                        error: function(xhr) {
                            // Remove loading overlay
                            $('.loading-overlay').remove();
                            let errorMessage =
                            'An error occurred while resetting the password.';
                            if (xhr.responseJSON && xhr.responseJSON.message) {
                                errorMessage = xhr.responseJSON.message;
                            }
                            toastr.error(errorMessage);
                        }
                    });
                }
            });
        });
    </script>
@endsection
