@extends('master')

@php
use App\Models\ImportFile;
@endphp

@section('content')
<div class="card card-custom mb-5">
    <div class="card-body" x-data="{ showFilter: false }">
        @if (session('status'))
        <div class="alert alert-success" role="alert">
            {{ session('status') }}
        </div>
        @endif

        @if (session('error'))
        <div class="alert alert-danger" role="alert">
            {{ session('error') }}
        </div>
        @endif

        <div class="row justify-content-between">
            <div class="col-12 col-sm-8 col-md-6 col-lg-4 col-xl-3 mb-6">
                <div class="input-icon">
                    <input type="text" class="form-control" placeholder="Search..." id="preview_search" />
                    <span>
                        <i class="flaticon2-search-1 text-muted"></i>
                    </span>
                </div>
            </div>
        </div>

        <div class="datatable datatable-bordered datatable-head-custom" id="preview_dt"></div>
    </div>
</div>

<!-- Script Preview Modal -->
<div class="modal fade" id="scriptPreviewModal" tabindex="-1" role="dialog" aria-labelledby="scriptPreviewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="scriptPreviewModalLabel">Script Preview</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <i class="ki ki-close"></i>
                </button>
            </div>
            <div class="modal-body">
                <div id="script-preview-content">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="sr-only">Loading...</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <a href="#" id="download-preview-btn" class="btn btn-primary">
                    <i class="fas fa-download"></i> Download
                </a>
                <a href="#" id="sign-preview-btn" class="btn btn-success">
                    <i class="fas fa-pen-nib"></i> Sign
                </a>
            </div>
        </div>
    </div>
</div>
@endsection

@section('styles')
<style>
    /* Custom styles for the preview modal */
    #scriptPreviewModal .modal-dialog {
        max-width: 95%;
        height: 95vh;
        margin: 0.5rem auto;
    }

    #scriptPreviewModal .modal-content {
        height: 100%;
        border-radius: 4px;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
        display: flex;
        flex-direction: column;
    }

    #scriptPreviewModal .modal-body {
        flex: 1;
        overflow: hidden;
        padding: 0;
    }

    #scriptPreviewModal .modal-header {
        border-bottom: 1px solid #ebedf3;
        padding: 1rem 1.75rem;
    }

    #scriptPreviewModal .modal-footer {
        border-top: 1px solid #ebedf3;
        padding: 1rem 1.75rem;
        position: relative;
        flex-shrink: 0;
        justify-content: flex-end;
        background-color: #fff;
        z-index: 5;
    }

    #scriptPreviewModal .close {
        cursor: pointer;
        font-size: 1.25rem;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 2rem;
        height: 2rem;
        margin: 0;
        padding: 0;
    }

    #scriptPreviewModal .close i {
        font-size: 1rem;
    }

    #scriptPreviewModal .close:hover {
        color: #3699FF;
        background-color: #f3f6f9;
        border-radius: 4px;
    }

    #script-preview-content {
        height: 100%;
        width: 100%;
        overflow: hidden;
        position: relative;
    }

    #script-preview-content iframe {
        width: 100%;
        height: 100%;
        border: none;
        display: block;
    }
</style>
@endsection

@section('scripts')
<script src="https://cdn.jsdelivr.net/gh/alpinejs/alpine@v2.7.0/dist/alpine.min.js" defer></script>
<script>
    var datatable;
    var datatableElement;
    var searchElement;
    var columnArray;

    // Convert PHP data to JavaScript
    const allRowsData = @json($allRowsData);

    // Function to capture client device time and submit the sign form
    function captureTimeAndSign(signRoute) {
        // Get the current date
        const now = new Date();

        // Create a complete date-time string with timezone information
        // Format: YYYY-MM-DD HH:MM:SS +/-HHMM
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const hours = String(now.getHours()).padStart(2, '0');
        const minutes = String(now.getMinutes()).padStart(2, '0');
        const seconds = String(now.getSeconds()).padStart(2, '0');

        // Get timezone offset in minutes and convert to hours and minutes
        const tzOffset = now.getTimezoneOffset();
        const tzOffsetHours = Math.abs(Math.floor(tzOffset / 60)).toString().padStart(2, '0');
        const tzOffsetMinutes = Math.abs(tzOffset % 60).toString().padStart(2, '0');
        const tzSign = tzOffset <= 0 ? '+' : '-'; // Note: getTimezoneOffset returns negative for positive UTC offsets

        // Construct the full datetime string with timezone
        const clientTimestamp =
            `${year}-${month}-${day} ${hours}:${minutes}:${seconds} ${tzSign}${tzOffsetHours}${tzOffsetMinutes}`;


        // Create a form to submit
        const form = $('<form>', {
            method: 'GET',
            action: signRoute
        });

        // Add client timestamp as a query parameter
        form.append($('<input>', {
            type: 'hidden',
            name: 'client_timestamp',
            value: clientTimestamp
        }));

        // Submit the form
        $('body').append(form);
        form.submit();
        form.remove();
    }

    datatableElement = $('#preview_dt');
    searchElement = $('#preview_search');

    columnArray = [{
            field: 'iteration',
            title: `#`,
            width: 50,
            sortable: true,
            autoHide: false,
        },
        {
            field: 'file_name',
            title: `File Name`,
            width: 250,
            sortable: true,
            autoHide: false,
        },
        {
            field: 'created_at',
            title: `Created Date`,
            width: 200,
            sortable: true,
            autoHide: false,
            //     template: function(data) {
            //     return data.created_at ? moment(data.created_at).format('DD/MM/YYYY HH:mm:ss') : '';
            // }
        },
        {
            field: 'script_date',
            title: `Script Date`,
            width: 120,
            sortable: true,
            autoHide: false,
        },
        {
            field: 'first_name',
            title: `First Name`,
            width: 120,
            sortable: true,
            autoHide: false,
        },
        {
            field: 'last_name',
            title: `Last Name`,
            width: 120,
            sortable: true,
            autoHide: false,
        },
        {
            field: 'medication',
            title: `Medication`,
            width: 150,
            sortable: true,
            autoHide: false,
        },
        {
            field: 'status',
            title: `Status`,
            width: 100,
            sortable: true,
            autoHide: false,
            template: function(row) {
                return `<span class="font-weight-bolder">${row.status}</span>`;
            }
        },
        {
            field: 'Actions',
            title: 'Actions',
            sortable: false,
            width: 'auto',
            overflow: 'visible',
            autoHide: false,
            template: function(row) {
                const downloadRoute = `{{ route('archive.file-download', ['id' => '::ID']) }}`.replace('::ID',
                    row.id);
                const viewRoute = `{{ route('archive.show-pdf', ['id' => '::ID']) }}`.replace('::ID', row.id);
                const signRoute = `{{ route('archive.sign-pdf', ['id' => '::ID']) }}`.replace('::ID', row.id);
                // const sendRoute = `{{ route('archive.send-fax', ['id' => '::ID']) }}`.replace('::ID', row.id);

                const status = row.status;

                let signButton = '';
                if ((status === '{{ ImportFile::STATUS_NEW }}' || status === '{{ ImportFile::STATUS_PENDING_REVISION }}') && row.is_eligible_for_signing) {
                    signButton = `<a href="#" data-sign-route="${signRoute}" class="btn btn-sm btn-clean btn-icon sign-btn" data-toggle="tooltip" title="Sign Script">
                                        <i class="menu-icon fas fa-pen-nib"></i>
                                      </a>`;
                } else {
                    signButton = `<a class="btn btn-sm btn-clean btn-icon disabled" data-toggle="tooltip" title="Already Signed">
                                        <i class="menu-icon fas fa-pen-nib text-muted"></i>
                                      </a>`;
                }

                // Add preview button
                const previewButton = `<a href="#" data-id="${row.id}"  data-is-signable="${row.is_eligible_for_signing}" data-view-route="${viewRoute}" data-download-route="${downloadRoute}" data-sign-route="${signRoute}" class="btn btn-sm btn-clean btn-icon preview-btn" data-toggle="tooltip" title="Preview Script">
                                            <i class="menu-icon fas fa-eye"></i>
                                          </a>`;

                return `
                        <a href="${downloadRoute}" class="btn btn-sm btn-clean btn-icon" data-toggle="tooltip" title="Download Script">
                            <i class="menu-icon fas fa-download"></i>
                        </a>
                        ${signButton}
                        ${previewButton}
                    `;
            },
        }
    ];

    // Process data for datatable
    const processedData = allRowsData.map((data, index) => {
        return {
            id: data.id,
            iteration: index + 1,
            file_name: data.import.file_name,
            created_at: moment(data.created_at).format('MM/DD/YYYY  hh:mm A'),
            script_date: moment(data.script_date).format('MM/DD/YYYY'),
            first_name: data.first_name,
            last_name: data.last_name,
            medication: data.medication,
            status: data.status,
            is_eligible_for_signing : data.is_eligible_for_signing
        };
    });

    datatable = datatableElement.KTDatatable({
        data: {
            type: 'local',
            source: processedData,
            pageSize: 10
        },
        sortable: true,
        pagination: true,
        search: {
            input: searchElement
        },
        layout: {
            customScrollbar: false,
            scroll: true,
        },
        columns: columnArray
    });

    // Add event listener for sign buttons
    $(document).on('click', '.sign-btn', function(e) {
        e.preventDefault();
        const signRoute = $(this).data('sign-route');
        captureTimeAndSign(signRoute);
    });

    // Add event listener for preview buttons
    $(document).on('click', '.preview-btn', function(e) {
        e.preventDefault();

        const fileId = $(this).data('id');
        const viewRoute = $(this).data('view-route');
        const isEligibleForSign = $(this).data('is-signable');
        const downloadRoute = $(this).data('download-route');
        const signRoute = $(this).data('sign-route');

        // Set the download button URL
        $('#download-preview-btn').attr('href', downloadRoute);

        // Store the file ID and routes for the sign and send buttons
        $('#sign-preview-btn').data('file-id', fileId);
        $('#sign-preview-btn').data('sign-route', signRoute);
        $('#send-preview-btn').data('file-id', fileId);

        // Get the row data to determine status
        let rowData = null;
        const allData = datatable.getDataSourceParam('data');

        // Find the row with matching ID
        if (allData && allData.length) {
            rowData = allData.find(item => item.id == fileId);
        }

        // If we couldn't find the row data, try an alternative approach
        if (!rowData) {
            // Try to get the status directly from the DOM
            const statusCell = $(this).closest('tr').find('td:contains("{{ ImportFile::STATUS_NEW }}"),td:contains("{{ ImportFile::STATUS_PENDING_REVISION }}")');

            // Handle buttons based on status
            if (statusCell.length > 0 && isEligibleForSign) {
                // Status is NEW
                $('#sign-preview-btn').removeClass('disabled').prop('disabled', false);
                $('#send-preview-btn').removeClass('disabled').prop('disabled', false);
            } else {
                // Status is not NEW
                $('#sign-preview-btn').addClass('disabled').prop('disabled', true)
                    .attr('title', 'Cannot sign at this stage');
                $('#send-preview-btn').addClass('disabled').prop('disabled', true)
                    .attr('title', 'Cannot send at this stage');
            }
        } else {
            // Handle buttons based on status from row data
            if ((rowData.status === '{{ ImportFile::STATUS_NEW }}' || rowData.status === '{{ ImportFile::STATUS_PENDING_REVISION }}') && isEligibleForSign) {
                // Show sign and send buttons for NEW status
                $('#sign-preview-btn').removeClass('disabled').prop('disabled', false);
                $('#send-preview-btn').removeClass('disabled').prop('disabled', false);
            } else {
                // Hide sign and send buttons for other statuses
                $('#sign-preview-btn').addClass('disabled').prop('disabled', true)
                    .attr('title', 'Cannot sign at this stage');
                $('#send-preview-btn').addClass('disabled').prop('disabled', true)
                    .attr('title', 'Cannot send at this stage');
            }
        }

        // Show the modal
        $('#scriptPreviewModal').modal('show');

        // Load the script preview
        $('#script-preview-content').html(
            '<div class="text-center"><div class="spinner-border" role="status"><span class="sr-only">Loading...</span></div></div>'
        );

        // Load the PDF in an iframe
        setTimeout(function() {
            $('#script-preview-content').html(
                `<iframe src="${viewRoute}" width="100%" height="100%" style="height: 90vh;" frameborder="0"></iframe>`
            );
        }, 500);
    });

    // Handle sign button click in the modal
    $('#sign-preview-btn').on('click', function() {
        const signRoute = $(this).data('sign-route');

        if (!signRoute) {
            console.error('No sign route found');
            return;
        }

        // Close the modal
        $('#scriptPreviewModal').modal('hide');

        // Call the captureTimeAndSign function
        captureTimeAndSign(signRoute);
    });

    // Handle send button click in the modal
    $('#send-preview-btn').on('click', function() {
        const fileId = $(this).data('file-id');

        if (!fileId) {
            console.error('No file ID found for sending');
            return;
        }

        // Close the modal
        $('#scriptPreviewModal').modal('hide');

        // Redirect to the send route (this would need to be implemented)
        // For now, we'll just sign the document which will set it to Pending Approval
        const signRoute = $('#sign-preview-btn').data('sign-route');
        if (signRoute) {
            captureTimeAndSign(signRoute);
        }
    });

    // Handle modal events
    $('#scriptPreviewModal').on('hidden.bs.modal', function() {
        // Clear the preview content when modal is closed
        $('#script-preview-content').html('');
    });
</script>
@endsection