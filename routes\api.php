<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware('auth:api')->get('/user', function (Request $request) {
    return $request->user();
});

Route::post('/uploadTest', 'App\Http\Controllers\TestController@test');
Route::post('/sendTest', 'App\Http\Controllers\TestController@sendTest');

Route::post('/test-dispensepro', 'App\Http\Controllers\TestController@testDispensePro');
Route::post('/test-dispensepro-providers', 'App\Http\Controllers\TestController@testDispenseProProviders');

Route::post('/void-order', 'App\Http\Controllers\TestController@voidOrder');
Route::post('void', 'App\Http\Controllers\TestController@void');


Route::post('/dispensepro-webhook', 'App\Http\Controllers\TestController@testDispensePro');
