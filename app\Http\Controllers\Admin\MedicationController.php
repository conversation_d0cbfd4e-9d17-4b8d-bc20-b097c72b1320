<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\GetDatatableRequest;
use App\Http\Resources\DataTableCollection;
use App\Models\Import;
use App\Models\Medication;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Schema;

class MedicationController extends Controller
{
    public function index()
    {
        $page_title = 'Medications';

        return view('medications.index', [
            'page_title' => $page_title,
        ]);
    }

    public function indexWeb(GetDatatableRequest $request)
    {

        // Filter imports to only show those belonging to the current user
        $data = Medication::query();

        $search = $request->input('query.search', '');
        $sort_order = $request->input('sort.sort', '');
        $sort_field = $request->input('sort.field', '');
        if ($search) {
            $query_search = "%" . $search . "%";

            $data->where(function ($query) use ($query_search) {
                $query->where('name', 'like', $query_search)
                    ->orWhere('ndc', 'like', $query_search)
                    ->orWhereRaw("DATE_FORMAT(created_at, '%m/%d/%Y') LIKE ?", $query_search);
            });
        }

        if ($sort_order && $sort_field) {
            $medicationColumns = Schema::getColumnListing((new Medication())->table);

            if (in_array($sort_field, $medicationColumns)) {
                $data->orderBy($sort_field, $sort_order);
            } else {
                // Default to latest if field doesn't exist
                $data->latest();
            }
        } else {
            $data->latest();
        }
        return new DataTableCollection($data->paginate($request->pagination['perpage'], ['*'], 'page', $request->pagination['page']));
    }

    public function create()
    {
        $has_back = route('medications.index');
        $page_title = 'Create Medication';


        $livewire_component = 'medication.create-edit';
        $livewire_data = [
            'page_title' => $page_title,
        ];

        return view('layouts.livewire', [
            'page_title' => $page_title,
            'has_back' => $has_back,
            'livewire_component' => $livewire_component,
            'livewire_data' => $livewire_data,
        ]);
    }

    public function edit(Medication $medication)
    {
        $has_back = route('medications.index');
        $page_title = 'Edit Medication';

        $livewire_component = 'medication.create-edit';
        $livewire_data = [
            'medication' => $medication,
            'page_title' => $page_title,
        ];

        return view('layouts.livewire', [
            'page_title' => $page_title,
            'has_back' => $has_back,
            'livewire_component' => $livewire_component,
            'livewire_data' => $livewire_data,
        ]);
    }

    public function delete(Medication $medication)
    {
        // Log medication deletion before deleting
        // LogService::logMedicationDeleted($medication);

        $medication->delete();
        return response([
            'message' => __('messages.medication_deleted'),
            'status' => '1',
        ]);
    }
    public function change_status(Medication $medication)
    {
        $medication->is_active = !$medication->is_active;
        $medication->save();

        return response([
            'message' => 'Updated Successfully',
            'status' => 1,
        ]);
    }
    
}
