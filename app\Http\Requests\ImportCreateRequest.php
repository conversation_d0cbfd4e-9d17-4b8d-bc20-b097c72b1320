<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ImportCreateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        //return {{model}}}::$getListRules;
        return [
            'provider_npi' => [
                'required',
                'digits:10',
                Rule::exists('users', 'NPI#')->where(function ($query) {
                    $query->where('role', 'provider');
                }),
            ],
            'medication_name' => [
                'required',
                Rule::exists('medications', 'name')->where(function ($query) {
                    $query->whereRaw('BINARY name = ?', [$this->medication_name]);
                }),
            ],
            'medication_ndc_csn'        => [
                'required',
                'max:64',
                'string',
                Rule::exists('medications', 'ndc')->where(function ($query) {
                    $query->where('name', $this->medication_name); // exact match, case-sensitive
                }),
            ],
            'rx_date' => 'required|date|date_format:Y-m-d',
            'patient_last_name' => 'required|string|max:256',
            'patient_first_name' => 'required|string|max:256',
            'patient_dob' => 'required|date|before:today|date_format:Y-m-d',
            'patient_gender' => 'nullable|string|in:M,F',
            'patient_address' => 'required|string|max:256',
            'patient_city' => 'required|string|max:256',
            'patient_state' => 'required|exists:states,short_name',
            'patient_zip_code' => 'required|string|max:20',
            'patient_phone' => 'nullable|digits_between:0,15',
            // 'stregnth' => 'required|string|max:50',
            // 'dosing' => 'required|integer|min:0',
            'medication_refills' => 'nullable|integer|min:0',
            'medication_vial_quantity' => 'nullable|integer|gt:0',
            'medication_days_supply' => 'nullable|integer|gt:0',
            'rx_sig' => 'nullable|string|max:1024',
            'rx_notes' => 'nullable|string|max:1024',
            'rx_ship_to' => [
                'required',
                Rule::exists('ship_to', 'name')->where(function ($query) {
                    $query->whereRaw('BINARY name = ?', [$this->rx_ship_to]);
                }),
            ],

            'rx_pdf' => 'required|file|mimes:pdf|max:10240', // 10MB max
            'transaction_id' => 'required|max:1024',



        ];
    }

    public function messages()
    {
        return [
            'npi.exists' => 'Invalid NPI number. Only users with provider role are allowed.',
        ];
    }
}
