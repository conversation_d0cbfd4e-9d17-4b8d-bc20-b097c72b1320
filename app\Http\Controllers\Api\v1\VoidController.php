<?php

namespace App\Http\Controllers\Api\v1;

use App\Http\Controllers\Controller;
use App\Http\Requests\VoidRequest;
use App\Models\ImportFile;
use App\Traits\DispenseProManager;
use Illuminate\Http\Request;

class VoidController extends Controller
{
    use  DispenseProManager;
    public function void(VoidRequest $request)
    {

        $orderId = $request->input('order_id');

        // Check if order_id exists in import_fields table and has status 'Sent'
        $importField = ImportFile::where('order_id', $orderId)->first();

        if (!$importField) {
            return response()->json([
                'message' => __('apiMessages.order_id_not_found'),
                'status' => '0',
            ]);
        }

        if ($importField->status !== ImportFile::STATUS_SENT) {
            return response()->json([
                'message' => __('apiMessages.script_not_in_sent_status'),
                'status' => '0',
            ]);
        }


        $results = self::DispenseProVoidOrder($orderId);

        $result = $results[0] ?? null;
        $response = $result['response'] ?? [];
        $status = $response['status'] ?? null;
        $errorMessages = $response['errorMessages'] ?? null;

        if ($status === 'ok' && (empty($errorMessages) || $errorMessages === [])) {
            $importField->status = ImportFile::STATUS_VOIDED;
            $importField->save();
        }
        else{
            return response()->json([
                'message' => __('apiMessages.void_failed'),
                'status' => '0',
            ]);
        }
        return response()->json([
            // 'data' => $results,
            'transaction_id' => $request->transaction_id,
            'message' => __('apiMessages.void_successfully'),
            'status' => '1',
        ]);
    }

    public function store(Request $request)
    {
        //
    }
}
