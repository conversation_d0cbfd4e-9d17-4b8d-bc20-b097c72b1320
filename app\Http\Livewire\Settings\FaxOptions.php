<?php

namespace App\Http\Livewire\Settings;

use App\Models\FaxNumbers;
use App\Services\LogService;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Livewire\Component;

class FaxOptions extends Component
{
    public $faxNumbers = [];
    public $newFaxNumber = '';
    public $newFaxInputs = []; // Array to store temporary new fax inputs

    protected $rules = [
        'faxNumbers.*.numbers' => 'required|string|min:7|max:12|regex:/^\d{7,12}$/',
        'faxNumbers.*.label' => 'required|string|max:225',
        'newFaxInputs.*.number' => 'required|string|min:7|max:12|regex:/^\d{7,12}$/',
        'newFaxInputs.*.label' => 'required|string|max:225',
    ];

    protected $messages = [
        'faxNumbers.*.numbers.required' => 'Fax number cannot be empty.',
        'faxNumbers.*.numbers.max' => 'Fax number cannot exceed 12 characters.',
        'faxNumbers.*.numbers.min' => 'Fax number must be at least 7 characters.',
        'faxNumbers.*.numbers.regex' => 'Fax number must be a valid number (7-12 digits only).',
        'faxNumbers.*.label.required' => 'Label is required.',
        'faxNumbers.*.label.max' => 'Label cannot exceed 225 characters.',

        'newFaxInputs.*.label' => 'Label is required.',
        'newFaxInputs.*.label.max' => 'Label cannot exceed 225 characters.',
        'newFaxInputs.*.number' => 'Fax number is required.',
        'newFaxInputs.*.number.max' => 'Fax number cannot exceed 12 characters.',
        'newFaxInputs.*.number.min' => 'Fax number must be at least 7 characters.',
    ];

    public function mount()
    {
        $this->loadFaxNumbers();
    }

    public function loadFaxNumbers()
    {
        $this->faxNumbers = FaxNumbers::where('is_active', 1)
            ->get()
            ->map(function ($fax) {
                return [
                    'id' => $fax->id,
                    'numbers' => $fax->numbers,
                    'label' => $fax->label,
                ];
            })
            ->toArray();
    }

    public function addNewFaxNumber()
    {
        // Just add a new empty input field to the temporary inputs array
        $this->newFaxInputs[] = [
            'number' => '',
            'label' => ''
        ];

        $this->emit('alert', ['type' => 'success', 'message' => __('messages.new_fax_number_field_added')]);
    }

    public function removeNewFaxInput($index)
    {
        if (isset($this->newFaxInputs[$index])) {
            unset($this->newFaxInputs[$index]);
            // Re-index the array to maintain proper indexing
            $this->newFaxInputs = array_values($this->newFaxInputs);

            // Clear related validation errors
            $this->resetErrorBag("newFaxInputs.$index.label");
            $this->resetErrorBag("newFaxInputs.$index.number");

            $this->emit('alert', ['type' => 'success', 'message' => __('messages.fax_number_field_removed')]);
        }
    }

    public function deleteFaxNumber($index)
    {
        if (isset($this->faxNumbers[$index])) {
            // Check if the fax number is empty
            if (empty($this->faxNumbers[$index]['numbers'])) {
                $this->emit('alert', ['type' => 'error', 'message' => __('messages.fax_number_deletion_error')]);
                return;
            }

            // If it's an existing record, delete from database
            if (isset($this->faxNumbers[$index]['id']) && $this->faxNumbers[$index]['id']) {
                $faxNumber = FaxNumbers::find($this->faxNumbers[$index]['id']);
                if ($faxNumber) {
                    // Log fax deletion before deleting
                    LogService::logFaxDeleted($faxNumber->numbers, $faxNumber->label);
                    $faxNumber->delete();
                }
            }

            // Remove from array
            unset($this->faxNumbers[$index]);
            $this->faxNumbers = array_values($this->faxNumbers); // reindex the array

            $this->emit('alert', ['type' => 'success', 'message' => __('messages.fax_number_deleted_successfully')]);
        }
    }

    public function store()
    {
        $this->validate();

        try {
            // Step 1: Check for duplicates within the current form data
            $allNumbers = [];
            foreach ($this->faxNumbers as $item) {
                if (!empty(trim($item['numbers']))) {
                    $allNumbers[] = trim($item['numbers']);
                }
            }

            foreach ($this->newFaxInputs as $item) {
                if (!empty(trim($item['number']))) {
                    $allNumbers[] = trim($item['number']);
                }
            }

            if (count($allNumbers) !== count(array_unique($allNumbers))) {
                $this->addError('duplicate_numbers', __('messages.duplicate_fax_numbers'));
                return;
            }

            // Step 2: Check existing fax numbers against database for duplicates (excluding their own records)
            foreach ($this->faxNumbers as $index => $faxData) {
                if (!empty(trim($faxData['numbers']))) {
                    $query = FaxNumbers::where('numbers', trim($faxData['numbers']))
                        ->where('is_active', 1);

                    // If updating existing record, exclude it from the check
                    if (isset($faxData['id']) && $faxData['id']) {
                        $query->where('id', '!=', $faxData['id']);
                    }

                    if ($query->exists()) {
                        $this->addError("faxNumbers.$index.numbers", 'This fax number already exists in the database.');
                        return;
                    }
                }
            }

            // Step 3: Check new fax inputs against database
            foreach ($this->newFaxInputs as $index => $input) {
                if (!empty(trim($input['number']))) {
                    $exists = FaxNumbers::where('numbers', trim($input['number']))
                        ->where('is_active', 1)
                        ->exists();
                    if ($exists) {
                        $this->addError("newFaxInputs.$index.number", 'This fax number already exists in the database.');
                        return;
                    }
                }
            }

            $savedCount = 0;

            // Process existing fax numbers
            foreach ($this->faxNumbers as $faxData) {
                if (!empty(trim($faxData['numbers'])) && !empty(trim($faxData['label']))) {
                    if (isset($faxData['id']) && $faxData['id']) {
                        // Update existing record
                        $originalFax = FaxNumbers::find($faxData['id']);
                        $changes = [];
                        if ($originalFax) {
                            if ($originalFax->numbers !== trim($faxData['numbers'])) {
                                $changes['old_number'] = $originalFax->numbers;
                                $changes['new_number'] = trim($faxData['numbers']);
                            }
                            if ($originalFax->label !== trim($faxData['label'])) {
                                $changes['old_label'] = $originalFax->label;
                                $changes['new_label'] = trim($faxData['label']);
                            }
                        }

                        FaxNumbers::where('id', $faxData['id'])
                            ->update([
                                'numbers' => trim($faxData['numbers']),
                                'label' => trim($faxData['label']),
                                'is_active' => 1
                            ]);

                        // Log fax update if there were changes
                        if (!empty($changes)) {
                            LogService::logFaxUpdated(trim($faxData['numbers']), trim($faxData['label']), $changes);
                        }
                        $savedCount++;
                    } else {
                        // Create new record
                        FaxNumbers::create([
                            'numbers' => trim($faxData['numbers']),
                            'label' => trim($faxData['label']),
                            'is_active' => 1
                        ]);

                        // Log fax addition
                        LogService::logFaxAdded(trim($faxData['numbers']), trim($faxData['label']));
                        $savedCount++;
                    }
                }
            }

            // Process new fax inputs
            foreach ($this->newFaxInputs as $newFaxData) {
                if (!empty(trim($newFaxData['number'])) && !empty(trim($newFaxData['label']))) {
                    FaxNumbers::create([
                        'numbers' => trim($newFaxData['number']),
                        'label' => trim($newFaxData['label']),
                        'is_active' => 1
                    ]);

                    // Log fax addition
                    LogService::logFaxAdded(trim($newFaxData['number']), trim($newFaxData['label']));
                    $savedCount++;
                }
            }

            // Clear the temporary inputs after saving
            $this->newFaxInputs = [];

            $this->emit('alert', ['type' => 'success', 'message' => __('messages.fax_saved', ['count' => $savedCount])]);
            $this->loadFaxNumbers(); // Reload to get fresh data with IDs

        } catch (ValidationException $e) {
            // Validation errors are already handled by Livewire
            $this->emit('alert', ['type' => 'error', 'message' => __('messages.fax_validation_errors')]);
        } catch (\Exception $e) {
            Log::error('Error saving fax numbers: ' . $e->getMessage(), [
                'faxNumbers' => $this->faxNumbers,
                'newFaxInputs' => $this->newFaxInputs,
                'trace' => $e->getTraceAsString()
            ]);
            $this->emit('alert', ['type' => 'error', 'message' => __('messages.error_saving_fax_numbers', ['error' => $e->getMessage()])]);
        }
    }

    public function updated($propertyName)
    {

        // Only validate the field if it has content
        $this->validateOnly($propertyName);

        // Emit contentChanged event to re-initialize Select2 after property updates
        $this->emit('contentChanged');
    }

    public function render()
    {
        return view('livewire.settings.fax-options');
    }
}
