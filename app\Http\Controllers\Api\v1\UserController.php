<?php

namespace App\Http\Controllers\Api\v1;

use App\Http\Controllers\Controller;
use App\Http\Requests\UserListRequest;
use App\Models\Patient;
use App\Models\Practice;
use App\Models\PracticeProvider;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class UserController extends Controller
{
    public function indexUsers(UserListRequest $request)
    {
        $practice = Auth::user();
        
        $data = User::query();
        $provider_ids = PracticeProvider::where('practice_id', $practice->id)->get()->pluck('provider_id')->toArray();

        $data = $data->whereIn('id', $provider_ids)
            ->select('first_name', 'last_name','NPI#')
            ->where('role', 'provider');


        // if ($request->has('search')) {
        //     $search = '%' . $request->search . '%';
        //     $data = $data->where(function ($query) use ($search) {
        //         $query->where('first_name', 'like', $search)
        //             ->orWhere('last_name', 'like', $search)
        //             ->orWhere('email', 'like', $search)
        //             ->orWhere('role', 'like', $search)
        //             ->orWhere('phone', 'like', $search)
        //             ->orWhere('address', 'like', $search)
        //             ->orWhere('city', 'like', $search)
        //             ->orWhere('zip', 'like', $search);
        //     });
        // }


        if ($request->has('page')) {

            return response()->json(
                collect([
                    'status' => '1',
                    'message' => __('apiMessages.users_list_returned'),
                    'transaction_id' => $request->transaction_id,
                ])->merge($data->simplePaginate($request->has('per_page') ? $request->per_page : 10))
            );
        }

        return response()->json([
            'data' => $data->get(),
            'transaction_id' => $request->transaction_id,
            'message' => __('apiMessages.users_list_returned'),
            'status' => '1',
        ]);
    }

    public function store(Request $request)
    {
        //
    }

    public function destroyPatient()
    {
        $patient = Patient::all();

        foreach ($patient as $p) {
            $p->delete();
        }

        return response()->json([
            'message' => __('apiMessages.patient_deleted_successfully'),
            'status' => 1,
        ]);
    }
}
