@extends('master')

@section('content')
    <div class="container-fluid">
        <div class="row justify-content-center">
            <div class="col-md-12">
                <div class="card card-custom">
                    <div class="text-end me-3">
                        <a href="/sample_files/Excel-template.xlsx" download="Excel-template.xlsx"
                            class="download-template">Download
                            Excel
                            template</a>
                    </div>

                    <div class="card-body">
                        <!-- Display no data error message -->
                        @if (session('no_data_error'))
                            <div class="fade show" role="alert" style="margin-bottom: 20px;">
                                <h3 style="color: red;">{{ session('no_data_error') }}</h3>
                            </div>
                        @endif

                        <div class="progress-steps-container mb-5">
                            <div class="progress-line">
                                <div class="progress-line-inner" style="width: {{ ($currentStep / $totalSteps) * 100 }}%;">
                                </div>
                            </div>
                            <div class="progress-steps">
                                <div class="progress-step {{ $currentStep >= 1 ? 'active' : '' }}">
                                    <div class="step-circle">1</div>
                                    <div class="step-label">Upload</div>
                                </div>
                                <div class="progress-step {{ $currentStep >= 2 ? 'active' : '' }}">
                                    <div class="step-circle">2</div>
                                    <div class="step-label">Create</div>
                                </div>
                                <div class="progress-step {{ $currentStep >= 3 ? 'active' : '' }}">
                                    <div class="step-circle">3</div>
                                    <div class="step-label">Done</div>
                                </div>
                            </div>
                        </div>

                        <div class="row justify-content-center">
                            <div class="col-md-8">
                                <h6 class="mb-3">Select Provider and Excel File</h6>
                                <form action="{{ route('excel.staff-bulk-import.store') }}" method="POST"
                                    enctype="multipart/form-data" id="staff-excel-form">
                                    @csrf

                                    {{-- <x-layout.col> --}}
                                    <!-- Provider Selection -->
                                    <div class="form-group mb-4">
                                        <label for="provider_id" class="form-label font-weight-bold">Provider <span class="text-danger">*</span></label>
                                        <select name="provider_id" id="provider_id"
                                            class="form-control @error('provider_id') is-invalid @enderror"
                                            data-width="100%">
                                            <option value="">Select a Provider</option>
                                            @foreach ($providers as $provider)
                                                <option value="{{ $provider->id }}"
                                                    {{ old('provider_id') == $provider->id ? 'selected' : '' }}>
                                                    {{ $provider->first_name }} {{ $provider->last_name }}
                                                    @if ($provider->clinic_name)
                                                        - {{ $provider->clinic_name }}
                                                    @endif
                                                </option>
                                            @endforeach
                                        </select>
                                        @error('provider_id')
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                        @enderror
                                        <small class="form-text text-muted">
                                            Select the provider who will own these imported scripts. The scripts will
                                            appear
                                            in their "Ready to Sign" section.
                                        </small>
                                    </div>
                                    {{-- </x-layout.col> --}}


                                    <!-- File Selection -->
                                    <div class="form-group mb-4">
                                        <label for="excel_file" class="form-label font-weight-bold">Excel File</label>
                                        <div class="custom-file">
                                            <input type="file" name="excel_file"
                                                class="custom-file-input @error('excel_file') is-invalid @enderror"
                                                id="excel_file" accept=".xlsx,.xls" style="opacity: 0; position: absolute;"
                                                required>
                                            <div class="d-flex">
                                                <input type="text" class="form-control mr-3" id="file_name_display"
                                                    placeholder="No file selected" readonly>
                                                <button type="button" class="btn btn-secondary"
                                                    onclick="document.getElementById('excel_file').click()">Browse</button>
                                            </div>
                                            @error('excel_file')
                                                <span class="invalid-feedback" role="alert">
                                                    <strong>{{ $message }}</strong>
                                                </span>
                                            @enderror
                                        </div>
                                        <small class="form-text text-muted">
                                            Please upload an Excel file (.xlsx or .xls) with prescription data.
                                        </small>
                                    </div>

                                    <!-- Submit Button -->
                                    <div class="d-flex justify-content-center mt-5">
                                        <button type="submit" class="btn btn-primary px-4" id="import-btn" style="display: none;">
                                            Import
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                        <div class="text-end me-3">
                            <a href="/sample_files/Template.xlsx" download="Template.xlsx"
                                class="download-template">Download
                                Excel
                                template</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <style>
        .card {
            border: none;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
            border-radius: 0;
        }

        .card-header {
            background-color: #fff;
            border-bottom: 1px solid #f1f1f1;
            padding: 15px 20px;
        }

        /* Progress Steps Styling */
        .progress-steps-container {
            position: relative;
            padding: 20px 0;
            margin: 0 auto;
            max-width: 700px;
        }

        .progress-steps {
            display: flex;
            justify-content: space-between;
            position: relative;
            z-index: 1;
        }

        .progress-step {
            text-align: center;
            width: 20%;
            position: relative;
        }

        .step-circle {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background-color: #e9ecef;
            color: #6c757d;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 8px;
            font-weight: 500;
            font-size: 14px;
        }

        .progress-step.active .step-circle {
            background-color: #000000;
            color: white;
        }

        .step-label {
            font-size: 13px;
            color: #6c757d;
            font-weight: 400;
        }

        .progress-step.active .step-label {
            color: #000000;
            font-weight: 500;
        }

        .progress-line {
            position: absolute;
            top: 38px;
            left: 10%;
            right: 10%;
            height: 2px;
            background-color: #e9ecef;
            z-index: 0;
        }

        .progress-line-inner {
            height: 100%;
            background-color: #000000;
        }

        .download-template {
            position: absolute;
            top: 20px;
            right: 30px;
            color: #6b21a8;
            font-weight: 500;
            text-decoration: underline;
            font-size: 16px;
            /* Increase this if you want it even bigger */
        }
    </style>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // File input change handler
            document.getElementById('excel_file').addEventListener('change', function(e) {
                var importBtn = document.getElementById('import-btn');
                if (e.target.files.length > 0) {
                    // Get just the filename without any path
                    var fileName = e.target.files[0].name;
                    document.getElementById('file_name_display').value = fileName;

                    // Show the import button
                    if (importBtn) {
                        importBtn.style.display = 'inline-block';
                        importBtn.disabled = false;
                    }
                } else {
                    // Hide the import button if no file is selected
                    if (importBtn) {
                        importBtn.style.display = 'none';
                    }
                    document.getElementById('file_name_display').value = '';
                }
            });

            // Form submission handler
            var excelForm = document.getElementById('staff-excel-form');
            if (excelForm) {
                excelForm.addEventListener('submit', function(e) {
                    var fileInput = document.getElementById('excel_file');
                    var providerSelect = document.getElementById('provider_id');

                    if (fileInput.files.length === 0) {
                        e.preventDefault();
                        alert('Please select an Excel file to import');
                        return false;
                    }

                    if (!providerSelect.value) {
                        e.preventDefault();
                        
                        return false;
                    }

                    // Show loading state
                    var submitButton = this.querySelector('#import-btn');
                    if (submitButton) {
                        submitButton.innerHTML =
                            '<span class="spinner-border spinner-border-sm mr-2" role="status" aria-hidden="true"></span> Importing...';
                        submitButton.disabled = true;
                    }
                });
            }
        });
    </script>
@endsection

@section('styles')
    @parent
    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <style>
        /* ...existing styles... */
        .select2-container .select2-selection--single {
            height: 38px;
            display: flex;
            align-items: center;
            padding: 6px 12px;
        }

        .select2-container .select2-selection--single .select2-selection__arrow {
            height: 100%;
            display: flex;
            align-items: center;
            padding-left: 8px;
            /* optional, spacing from text */
        }
    </style>
@endsection

@section('scripts')
    @parent
    <!-- Select2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize Select2 for provider dropdown
            $('#provider_id').select2({
                placeholder: 'Select a Provider',
                // allowClear: true,
                // width: '100%'
            });
        });
    </script>
@endsection
