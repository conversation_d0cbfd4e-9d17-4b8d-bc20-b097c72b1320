<div class="step-content">
    <div class="alert alert-info">
        <strong>{{ $rowsCount }} rows</strong> successfully read, <strong>{{ $errorCount }}</strong> errors
    </div>

    <div class="table-responsive">
        <table class="table table-bordered table-striped">
            <thead class="thead-dark">
                <tr>
                    <th>#</th>
                    <th>Script Date</th>
                    <th>Last Name</th>
                    <th>First Name</th>
                    <th>Medication</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>
                @foreach ($excelData as $index => $row)
                    <tr class="{{ in_array($index + 6, $errorRows) ? 'error-row' : '' }}">
                        <td>{{ $index + 1 }}</td>
                        <td>
                            @if (!empty($row[0]))
                                @if (is_numeric($row[0]))
                                    {{ \PhpOffice\PhpSpreadsheet\Shared\Date::excelToDateTimeObject($row[0])->format('m/d/Y') }}
                                @else
                                    {{ $row[0] }}
                                @endif
                            @endif
                        </td>
                        <td>{{ $row[1] ?? '' }}</td>
                        <td>{{ $row[2] ?? '' }}</td>
                        <td>{{ $row[10] ?? '' }}</td>
                        <td>
                            @if (in_array($index + 6, $errorRows))
                                <span class="error-dot" data-toggle="tooltip" data-placement="top"
                                    title="This row contains errors and will be skipped during import"></span>
                            @else
                                <span class="badge badge-success">Valid</span>
                            @endif
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>
</div>

<style>
    /* Error dot styling */
    .error-dot {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: #dc3545;
        margin-left: 5px;
        position: relative;
        cursor: pointer;
    }

    /* Error row styling */
    .error-row {
        background-color: rgba(220, 53, 69, 0.1) !important;
    }

    .error-row:hover {
        background-color: rgba(220, 53, 69, 0.15) !important;
    }
</style>

<script>
    document.addEventListener('livewire:load', function() {
        // Initialize tooltips
        $('[data-toggle="tooltip"]').tooltip({
            container: 'body',
            html: true
        });
    });
</script>
