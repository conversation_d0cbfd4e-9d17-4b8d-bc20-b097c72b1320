<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PracticeMedication extends Model
{
    use HasFactory;

    //TABLE
    public $table = 'practice_medications';

    //FILLABLES
    protected $fillable = [
        'practice_id',
        'medication_id',
    ];

    //HIDDEN
    protected $hidden = [];

    //APPENDS
    protected $appends = [];

    //WITH
    protected $with = [];

    //CASTS
    protected $casts = [];

    //RULES
    public static $getListRules=[];
    

    public function practice()
    {
        return $this->belongsTo(Practice::class, 'practice_id', 'id');
    }

    public function medication()
    {
        return $this->belongsTo(Medication::class, 'medication_id', 'id');
    }

    // public function medication()
    // {
    //     return $this->belongsToMany(Medication::class, 'practice_medications', 'medication_id', 'practice_id')
    //         ->withPivot('medication_id')
    //         ->withTimestamps();
    // }
    //RELATIONSHIPS
    //public function example(){
    //    return $this->hasMany();
    //}

    //ATTRIBUTES
    //public function getExampleAttribute()
    //{
    //    return $data;
    //}
}
