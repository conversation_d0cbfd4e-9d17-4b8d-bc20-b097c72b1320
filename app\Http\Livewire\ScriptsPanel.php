<?php

namespace App\Http\Livewire;

use App\Models\Job;
use App\Services\ProgressTrackingService;
use Livewire\Component;

class ScriptsPanel extends Component
{

    public $processed = 0;
    public $total = 0;
    public $title = '';
    public $jobs = [];

    public function pollProgress()
    {
        $this->jobs = Job::whereNotNull('title')->get();
        $this->title = optional($this->jobs->first())->title ?? '';
        
        $progressTrackingService = new ProgressTrackingService();
        $progress = $progressTrackingService->get("queue_progress", [
            'total' => 0,
            'processed' => 0,
        ]);

        $this->total = $progress['total'];
        $this->processed = $progress['processed'];

        if ($this->processed >= $this->total && $this->total > 0) {
            $this->total = 0;
            $this->processed = 0;
        }
    }
    public function render()
    {
        return view('livewire.scripts-panel');
    }
}
